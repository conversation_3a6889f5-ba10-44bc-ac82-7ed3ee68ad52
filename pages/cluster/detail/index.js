import _ from 'lodash';
import {decorators, DetailPage, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {AppDetailPage, AppSidebar} from '@baidu/sui-biz';
import {OutlinedImport} from '@baidu/sui-icon';
import {Loading, Select} from '@baidu/sui';
import State from '../../../common/state';
import {checkWhiteByName} from '../../../common/white';
import {getQueryByName} from '../../../utils/util';
import {clusterDetailTransformV2ToV1} from '../../../utils/v1-to-v2';
import {actives as ACTIVES} from './link-config';
import {store} from 'san-store';
import {
    detailIcon,
    groupIcon,
    namespaceIcon,
    aiIcon,
    workloadIcon,
    hpaIcon,
    networkIcon,
    configIcon,
    storageIcon,
    inspecIcon,
    opIcon,
    secureIcon,
} from './sidebar-icon/';
import {ClusterStatus, PermissionType, ClusterType} from '../../../utils/enums';
import {checkAscendCluster, checkClusterV2} from '../../../utils/service';
import NamespaceList from '../namespace/list/list';
import ClusterMonitor from '../detail/monitor/cluster/detail';
import DeploymentList from '../../workload/deployment/list/list';
import JobList from '../../workload/job/list/list';
import CronJobList from '../../workload/cronjob/list/list';
import DaemonsetList from '../../workload/daemonset/list/list';
import StatefulsetList from '../../workload/statefulset/list/list';
import PodList from '../../workload/pod/list/list';
import DeploymentList2 from '../../workload/deployment2/index';
import JobList2 from '../../workload/job/list';
import CronJobList2 from '../../workload/cronjob/list';
import HpaList from '../../hpa/index';
import ConfigMapList from '../../storage/configmap/list/list';
import SecretList from './secret-dict/list';
import StorageClassList from '../../storage/storageclass/list/list';
import PvList from '../../storage/pv/list/list';
import PvcList from '../../storage/pvc/list/list';
import NodePools from '../../service-group/node-pools/list';
import AiJobList from '../../ai/aijob/list/list';
import AiQueueList from '../../ai/aiqueue/list/list';
import AiDatasetList from '../../ai/dataset/list/list';
import IngressList from './flow/ingress/list/list';
import ResourceObj from './resource-obj';
import Inspec from '../om-manage/inspec';
import ClusterCheck from '../om-manage/check';
import ClusterDiag from '../om-manage/diag';
import FlowServiceList from './flow/service/list/list';
import {linkConfig, getActive} from './link-config';
import BackUpList from './backup';
import './detail';
import './monitor';
import './flow';
import './events';
import './audit/list';
import './audit/open';
import './audit/overview';
import './restore';
import {MasterType, NetworkMode} from '../../../components/cluster/create-v2/enums';
import {BEC_REGION} from '../../cluster/create-v2/components/cluster-config';
import {inspecGuide, newSideBar} from '../../../components/common/feature-guide';
import {CceGroupWrap} from './cce-group-wrap';
import DetailWrap from './detail-wrap';

// 详情中列表的筛选缓存，导入到对应的组件中使用
export const tableFilterCache = {
    secretList: null, // 保密字典列表
    flowServiceList: null, // 服务列表
};

const {invokeAppComp, asPage, invokeBceSanUI, invokeComp} = decorators;
const $flag = ServiceFactory.resolve('$flag');

@asPage(
    '/cce/cluster/detail',
    '/cce/cluster/events',
    '/cce/cluster/audit',
    '/cce/application/namespace/list',
    '/cce/cluster/autoscale',
    '/cce/cluster/report',
    '/cce/cluster/vk',
    '/cce/application/deployment/list',
    '/cce/application/pod/list',
    '/cce/application/job/list',
    '/cce/application/cronjob/list',
    '/cce/application/daemonset/list',
    '/cce/application/statefulset/list',
    '/cce/application/hpa/list',
    '/cce/flow/service/list',
    '/cce/flow/ingress/list',
    '/cce/application/configmap/list',
    '/cce/application/secret/list',
    '/cce/application/storageclass/list',
    '/cce/application/pv/list',
    '/cce/application/pvc/list',
    '/cce/monitor/cluster',
    '/cce/monitor/container',
    '/cce/monitor/portrait',
    '/cce/instance/list',
    '/cce/master/list',
    '/cce/node-label/list',
    '/cce/cluster/group',
    '/cce/nodepools/list',
    '/cce/batch/deployments/list',
    '/cce/automation/access/list',
    '/cce/application/batch-deployments/list',
    '/cce/ai/aijob/list',
    '/cce/ai/aiqueue/list',
    '/cce/ai/dataset/list',
    '/cce/ai/helm/release',
    '/cce/application/deployment2',
    '/cce/monitor/log/center',
    '/cce/cluster/resource',
    '/cce/cluster/backup',
    '/cce/cluster/inspec',
    '/cce/cluster/check',
    '/cce/cluster/diag',
)
@invokeComp(
    '@cce-page-title',
    '@cce-cluster-detail',
    '@cce-cluster-events',
    '@cce-cluster-audit',
    '@cce-audit-log-open',
    '@cce-event-center',
    '@cce-monitor-container',
    '@cce-cluster-auto-scale-detail',
    '@cce-cluster-report',
    '@cce-instance-list',
    '@cce-worker-list',
    '@cce-master-list',
    '@cce-node-label-list',
    '@cce-vk-list',
    '@cce-automation-access-list',
    '@cce-batch-deployments-list',
    '@cce-helm-components',
    '@cce-cluster-monitor-log-center',
    '@cluster-not-found',
)
@invokeAppComp
@invokeBceSanUI
export default class ClusterDetail extends DetailPage {
    static pageName = 'cce-cluster-detail';
    REGION_CHANGE_LOCATION = '#/cce/cluster/list';
    static template = html`
        <s-app-detail-page>
            <div slot="pageTitle" class="app-page-title">
                <cce-page-title title-data="{=titleData=}" />
                <div
                    class="back-old-version"
                    on-click="toggleSideBarVersion"
                    s-if="(isClusterV2 || isARM) && !isCloudEdge && !isServerless"
                >
                    <s-icon-import />{{isOldSideBar ? '使用新版' : '返回旧版'}}
                </div>
            </div>
            <div class="detail-main">
                <template s-if="!detailLoading">
                    <template
                        s-if="isOldSideBar || !isClusterV2 || isServerless === true || isCloudEdge === true"
                    >
                        <s-sidebar title="" active-name="{=active=}">
                            <s-sidebar-item title="集群详情" name="detail" />
                            <s-sidebar-item
                                title="节点管理"
                                visible="{{isClusterV2 === true && isServerless === false}}"
                            >
                                <s-sidebar-item title="Worker" name="instance" />
                                <s-sidebar-item title="Master" name="master" />
                                <s-sidebar-item
                                    title="节点组"
                                    name="group"
                                    visible="{{isCloudEdge === false && !isNewBec}}"
                                />
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="节点列表"
                                name="instance"
                                visible="{{isClusterV2 === false && !isNewBec}}"
                            />
                            <s-sidebar-item
                                title="单元化部署"
                                visible="{{isCloudEdge === true && !isNewBec}}"
                            >
                                <s-sidebar-item title="边缘节点组" name="nodeGroups" />
                                <s-sidebar-item title="应用部署组" name="batchDeployments" />
                                <s-sidebar-item title="服务访问组" name="access" />
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="虚拟节点"
                                name="vk"
                                visible="{{$flag.CceMenuVk && !isClusterV2 && !isNewBec}}"
                            />
                            <s-sidebar-item
                                title="集群事件"
                                name="events"
                                visible="{{!isClusterV2 && !isNewBec}}"
                            />
                            <s-sidebar-item
                                title="集群审计"
                                name="audit"
                                visible="{{$flag.CceClusterAudit && auditWhite && !isNewBec}}"
                            />
                            <s-sidebar-item title="命名空间" name="namespace" />
                            <s-sidebar-item
                                title="自动伸缩"
                                name="autoscale"
                                visible="{{!isNewBec && !(isClusterV2 || isServerless === undefined || isServerless === true || isCloudEdge === undefined || isCloudEdge === true)}}"
                            />
                            <s-sidebar-item
                                title="云原生AI"
                                name="aihpc"
                                visible="{{isClusterV2 && !isAscendCluster && isServerless == false && isCloudEdge === false}}"
                            >
                                <s-sidebar-item
                                    visible="{{!isNewBec}}"
                                    title="队列管理"
                                    name="aiqueue"
                                />
                                <s-sidebar-item
                                    visible="{{!isNewBec}}"
                                    title="任务管理"
                                    name="aijob"
                                />
                                <s-sidebar-item title="数据集" name="dataset" />
                            </s-sidebar-item>
                            <s-sidebar-item title="工作负载">
                                <s-sidebar-item title="无状态部署" name="deployment" />
                                <s-sidebar-item title="容器组" name="pod" />
                                <s-sidebar-item title="普通任务" name="job" />
                                <s-sidebar-item title="定时任务" name="cronjob" />
                                <s-sidebar-item title="守护进程" name="daemonset" />
                                <s-sidebar-item title="有状态部署" name="statefulset" />
                                <s-sidebar-item
                                    title="自动伸缩"
                                    visible="{{$flag.CceMenuHpa && isCloudEdge === false}}"
                                    name="hpa"
                                />
                            </s-sidebar-item>
                            <s-sidebar-item title="流量接入">
                                <s-sidebar-item title="服务" name="flowService" />
                                <s-sidebar-item
                                    title="Ingress"
                                    name="flowIngress"
                                    style="display: {{ingressWhite && isCloudEdge === false && !isNewBec ? 'block' : 'none'}}"
                                />
                            </s-sidebar-item>
                            <s-sidebar-item title="存储配置">
                                <s-sidebar-item title="配置项" name="configmap" />
                                <s-sidebar-item title="保密字典" name="secret" />
                                <s-sidebar-item
                                    title="存储类"
                                    name="storageclass"
                                    visible="{{isCloudEdge === false}}"
                                />
                                <s-sidebar-item
                                    title="存储卷"
                                    name="pv"
                                    visible="{{isCloudEdge === false}}"
                                />
                                <s-sidebar-item
                                    title="存储声明"
                                    name="pvc"
                                    visible="{{isCloudEdge === false}}"
                                />
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="服务画像"
                                name="clusterReport"
                                visible="{{$flag.CceClusterReport && isCloudEdge === false && !isNewBec}}"
                            />
                            <s-sidebar-item
                                title="组件管理"
                                visible="{{isClusterV2 && isServerless == false && isCloudEdge === false}}"
                                name="aihelm"
                            />
                            <s-sidebar-item
                                title="监控日志"
                                visible="{{isCloudEdge === false && $flag.CceClusterMonitor && !isNewBec}}"
                            >
                                <s-sidebar-item title="集群监控" name="monitorCluster" />
                                <s-sidebar-item
                                    title="Prometheus监控"
                                    name="monitorContainer"
                                    visible="{{isClusterV2 && !isARM && containerWhite && isServerless === false && !$flag.CceSupportXS}}"
                                />
                                <s-sidebar-item
                                    title="日志中心"
                                    name="monitorLogCenter"
                                    visible="{{isClusterV2}}"
                                    disabled="{{blsDisabled}}"
                                >
                                    <div slot="title" class="has-disabled-title">
                                        <span>日志中心</span>
                                        <span s-if="{{blsDisabled}}" class="disabled-logo"
                                            ><span>未开服</span></span
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    title="事件中心"
                                    name="monitorPortrait"
                                    visible="{{!!$flag.CceMenuMonitorEvent}}"
                                />
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="资源对象浏览器"
                                visible="{{isClusterV2 && isServerless == false && isCloudEdge === false && !isNewBec && !isARM}}"
                                name="resourceObj"
                            >
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="应用备份"
                                name="backup"
                                visible="{{isClusterV2 && !isNewBec}}"
                            >
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="运维管理"
                                visible="{{enableInspecRegion && isClusterV2 && isServerless == false && isCloudEdge === false && !isARM}}"
                            >
                                <s-sidebar-item title="集群巡检" name="inspec" id="cluster-inspec">
                                    <div slot="title">集群巡检</div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    title="集群检查"
                                    name="clusterCheck"
                                    id="cluster-check"
                                >
                                    <div slot="title">集群检查</div>
                                </s-sidebar-item>
                                <s-sidebar-item title="故障诊断" name="diag" id="diag">
                                    <div slot="title" class="has-new-title">
                                        故障诊断
                                        <div class="new-logo"><span>NEW</span></div>
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>
                        </s-sidebar>
                    </template>
                    <!--TODO：上面的菜单上线一个月后删除-->
                    <template s-else>
                        <s-sidebar active-name="{=active=}" class="new-detail-sidebar">
                            <s-sidebar-item name="detail">
                                <template slot="title">
                                    <span class="icon">${detailIcon}</span>
                                    <a class="name-link" href="{{'detail'| getUrlByName}}"
                                        >集群详情</a
                                    >
                                </template>
                            </s-sidebar-item>
                            <s-sidebar-item
                                visible="{{isClusterV2 === true && isServerless === false}}"
                                expand
                            >
                                <template slot="title">
                                    <span class="icon">${groupIcon}</span>
                                    <span>节点管理</span>
                                </template>

                                <s-sidebar-item
                                    title="节点组"
                                    name="group"
                                    visible="{{isCloudEdge === false && !isNewBec}}"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'group'| getUrlByName}}"
                                            >节点组</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="节点" name="instance">
                                    <div slot="title">
                                        <a class="name-link" href="{{'instance'| getUrlByName}}"
                                            >节点</a
                                        >
                                    </div>
                                </s-sidebar-item>

                                <s-sidebar-item
                                    title="Master"
                                    name="master"
                                    visible="{{!isManaged}}"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'master'| getUrlByName}}"
                                            >Master</a
                                        >
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="节点列表"
                                name="instance"
                                visible="{{isClusterV2 === false && !isNewBec}}"
                            >
                                <div slot="title">
                                    <a class="name-link" href="{{'instance'| getUrlByName}}"
                                        >节点列表</a
                                    >
                                </div>
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="单元化部署"
                                visible="{{isCloudEdge === true && !isNewBec}}"
                            >
                                <s-sidebar-item title="边缘节点组" name="nodeGroups">
                                    <div slot="title">
                                        <a class="name-link" href="{{'nodeGroups'| getUrlByName}}"
                                            >边缘节点组</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="应用部署组" name="batchDeployments">
                                    <div slot="title">
                                        <a
                                            class="name-link"
                                            href="{{'batchDeployments'| getUrlByName}}"
                                            >应用部署组</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="服务访问组" name="access">
                                    <div slot="title">
                                        <a class="name-link" href="{{'access'| getUrlByName}}"
                                            >服务访问组</a
                                        >
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="虚拟节点"
                                name="vk"
                                visible="{{$flag.CceMenuVk && !isClusterV2 && !isNewBec}}"
                            >
                                <div slot="title">
                                    <a class="name-link" href="{{'vk'| getUrlByName}}">虚拟节点</a>
                                </div>
                            </s-sidebar-item>
                            <s-sidebar-item
                                title="集群事件"
                                name="events"
                                visible="{{!isClusterV2 && !isNewBec}}"
                            >
                                <div slot="title">
                                    <a class="name-link" href="{{'events'| getUrlByName}}"
                                        >集群事件</a
                                    >
                                </div>
                            </s-sidebar-item>
                            <s-sidebar-item name="namespace">
                                <template slot="title">
                                    <span class="icon">${namespaceIcon}</span>
                                    <a class="name-link" href="{{'namespace'| getUrlByName}}"
                                        >命名空间与配额</a
                                    >
                                </template>
                            </s-sidebar-item>
                            <div class="divider"></div>

                            <s-sidebar-item
                                title="自动伸缩"
                                name="autoscale"
                                visible="{{!isNewBec && !(isClusterV2 || isServerless === undefined || isServerless === true
                            || isCloudEdge === undefined || isCloudEdge === true)}}"
                            >
                                <div slot="title">
                                    <a class="name-link" href="{{'autoscale'| getUrlByName}}"
                                        >自动伸缩</a
                                    >
                                </div>
                            </s-sidebar-item>
                            <s-sidebar-item
                                name="aihpc"
                                visible="{{isClusterV2 && !isAscendCluster && isServerless == false && isCloudEdge === false}}"
                            >
                                <template slot="title">
                                    <span class="icon">${aiIcon}</span>
                                    <span>云原生AI</span>
                                </template>
                                <s-sidebar-item
                                    visible="{{!isNewBec}}"
                                    title="队列管理"
                                    name="aiqueue"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'aiqueue'| getUrlByName}}"
                                            >队列管理</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    visible="{{!isNewBec}}"
                                    title="任务管理"
                                    name="aijob"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'aijob'| getUrlByName}}"
                                            >任务管理</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="数据集" name="dataset">
                                    <div slot="title">
                                        <a class="name-link" href="{{'dataset'| getUrlByName}}"
                                            >数据集</a
                                        >
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>
                            <s-sidebar-item expand>
                                <template slot="title">
                                    <span class="icon">${workloadIcon}</span>
                                    <span>工作负载</span>
                                </template>
                                <s-sidebar-item title="无状态" name="deployment">
                                    <div slot="title">
                                        <a class="name-link" href="{{'deployment'| getUrlByName}}"
                                            >无状态</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="有状态" name="statefulset">
                                    <div slot="title">
                                        <a class="name-link" href="{{'statefulset'| getUrlByName}}"
                                            >有状态</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="守护进程集" name="daemonset">
                                    <div slot="title">
                                        <a class="name-link" href="{{'daemonset'| getUrlByName}}"
                                            >守护进程集</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="任务" name="job">
                                    <div slot="title">
                                        <a class="name-link" href="{{'job'| getUrlByName}}">任务</a>
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="定时任务" name="cronjob">
                                    <div slot="title">
                                        <a class="name-link" href="{{'cronjob'| getUrlByName}}"
                                            >定时任务</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="容器组" name="pod">
                                    <div slot="title">
                                        <a class="name-link" href="{{'pod'| getUrlByName}}"
                                            >容器组</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    visible="{{isClusterV2 && isServerless == false && isCloudEdge === false && !isNewBec && !isARM}}"
                                    name="resourceObj"
                                    id="resource-obj"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'resourceObj'| getUrlByName}}"
                                            >自定义资源</a
                                        >
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>
                            <s-sidebar-item
                                visible="{{$flag.CceMenuHpa && isCloudEdge === false}}"
                                name="hpa"
                            >
                                <div slot="title" class="has-new-title">
                                    <span class="icon">${hpaIcon}</span>
                                    <a class="name-link" href="{{'hpa'| getUrlByName}}">自动伸缩</a>
                                </div>
                            </s-sidebar-item>
                            <s-sidebar-item expand="{{expandByFeatureGuide}}" id="network">
                                <template slot="title">
                                    <span class="icon">${networkIcon}</span>
                                    <span>网络</span>
                                </template>
                                <s-sidebar-item title="服务" name="flowService">
                                    <div slot="title">
                                        <a class="name-link" href="{{'flowService'| getUrlByName}}"
                                            >服务</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    title="路由"
                                    name="flowIngress"
                                    style="display: {{ingressWhite && isCloudEdge === false && !isNewBec ? 'block' : 'none'}}"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'flowIngress'| getUrlByName}}"
                                            >路由</a
                                        >
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>
                            <s-sidebar-item id="config-manage">
                                <template slot="title">
                                    <span class="icon">${configIcon}</span>
                                    <span>配置管理</span>
                                </template>
                                <s-sidebar-item title="配置项" name="configmap">
                                    <div slot="title">
                                        <a class="name-link" href="{{'configmap'| getUrlByName}}"
                                            >配置项</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="保密字典" name="secret">
                                    <div slot="title">
                                        <a class="name-link" href="{{'secret'| getUrlByName}}"
                                            >保密字典</a
                                        >
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>
                            <s-sidebar-item id="cce-storage">
                                <template slot="title">
                                    <span class="icon">${storageIcon}</span>
                                    <span>存储</span>
                                </template>
                                <s-sidebar-item
                                    title="存储声明"
                                    name="pvc"
                                    visible="{{isCloudEdge === false}}"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'pvc'| getUrlByName}}"
                                            >存储声明</a
                                        >
                                    </div>
                                </s-sidebar-item>

                                <s-sidebar-item
                                    title="存储卷"
                                    name="pv"
                                    visible="{{isCloudEdge === false}}"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'pv'| getUrlByName}}"
                                            >存储卷</a
                                        >
                                    </div>
                                </s-sidebar-item>

                                <s-sidebar-item
                                    title="存储类"
                                    name="storageclass"
                                    visible="{{isCloudEdge === false}}"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'storageclass'| getUrlByName}}"
                                            >存储类</a
                                        >
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>
                            <div class="divider"></div>
                            <s-sidebar-item
                                visible="{{enableInspecRegion && isClusterV2 && isServerless == false && isCloudEdge === false && !isARM}}"
                                id="cce-inspec"
                            >
                                <template slot="title">
                                    <span class="icon">${inspecIcon}</span>
                                    <span>巡检与诊断</span>
                                </template>
                                <s-sidebar-item title="集群巡检" name="inspec" id="cluster-inspec">
                                    <div slot="title">
                                        <a class="name-link" href="{{'inspec'| getUrlByName}}"
                                            >集群巡检</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    title="集群检查"
                                    name="clusterCheck"
                                    id="cluster-check"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'clusterCheck'| getUrlByName}}"
                                            >集群检查</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item title="故障诊断" name="diag" id="diag">
                                    <div slot="title" class="has-new-title">
                                        <a class="name-link" href="{{'diag'| getUrlByName}}"
                                            >故障诊断</a
                                        >
                                        <div class="new-logo"><span>NEW</span></div>
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>

                            <s-sidebar-item
                                visible="{{isClusterV2}}"
                                expand="{{expandByFeatureGuide}}"
                                id="opManage"
                            >
                                <template slot="title">
                                    <span class="icon">${opIcon}</span>
                                    <span>运维与管理</span>
                                </template>
                                <s-sidebar-item
                                    title="事件中心"
                                    name="monitorPortrait"
                                    visible="{{!!$flag.CceMenuMonitorEvent && !isNewBec}}"
                                >
                                    <div slot="title">
                                        <a
                                            class="name-link"
                                            href="{{'monitorPortrait'| getUrlByName}}"
                                            >事件中心</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    title="Prometheus监控"
                                    name="monitorContainer"
                                    visible="{{isClusterV2 && !isARM && containerWhite && isServerless === false && !$flag.CceSupportXS && !isNewBec}}"
                                >
                                    <div slot="title">
                                        <a
                                            class="name-link"
                                            href="{{'monitorContainer'| getUrlByName}}"
                                            >Prometheus监控</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    visible="{{!isNewBec}}"
                                    title="集群监控"
                                    name="monitorCluster"
                                >
                                    <div slot="title">
                                        <a
                                            class="name-link"
                                            href="{{'monitorCluster'| getUrlByName}}"
                                            >集群监控</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    title="日志中心"
                                    name="monitorLogCenter"
                                    visible="{{isClusterV2 && !isNewBec}}"
                                    disabled="{{blsDisabled}}"
                                >
                                    <div slot="title" class="has-disabled-title">
                                        <a
                                            class="name-link"
                                            href="{{'monitorLogCenter'| getUrlByName}}"
                                            >日志中心</a
                                        >
                                        <span s-if="{{blsDisabled}}" class="disabled-logo"
                                            ><span>未开服</span></span
                                        >
                                    </div>
                                </s-sidebar-item>

                                <s-sidebar-item
                                    title="组件管理"
                                    visible="{{isClusterV2 && isServerless == false && isCloudEdge === false}}"
                                    name="aihelm"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'aihelm'| getUrlByName}}"
                                            >组件管理</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    visible="{{!isNewBec}}"
                                    title="应用备份"
                                    name="backup"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'backup'| getUrlByName}}"
                                            >应用备份</a
                                        >
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>

                            <s-sidebar-item
                                expand="{{expandByFeatureGuide}}"
                                id="secure"
                                visible="{{!isNewBec && ($flag.CceClusterAudit && auditWhite || $flag.CceClusterReport && isCloudEdge === false)}}"
                            >
                                <template slot="title">
                                    <span class="icon">${secureIcon}</span>
                                    <span>安全管理</span>
                                </template>
                                <s-sidebar-item title="角色" name="role">
                                    <div slot="title">
                                        <a class="name-link" href="{{'role'| getUrlByName}}"
                                            >角色</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    title="集群审计"
                                    name="audit"
                                    visible="{{$flag.CceClusterAudit && auditWhite && !isNewBec}}"
                                >
                                    <div slot="title">
                                        <a class="name-link" href="{{'audit'| getUrlByName}}"
                                            >集群审计</a
                                        >
                                    </div>
                                </s-sidebar-item>
                                <s-sidebar-item
                                    title="服务画像"
                                    name="clusterReport"
                                    visible="{{$flag.CceClusterReport && isCloudEdge === false && !isNewBec}}"
                                >
                                    <div slot="title">
                                        <a
                                            class="name-link"
                                            href="{{'clusterReport'| getUrlByName}}"
                                            >服务画像</a
                                        >
                                    </div>
                                </s-sidebar-item>
                            </s-sidebar-item>

                            <div class="placeholder"></div>
                        </s-sidebar>
                    </template>
                </template>

                <div class="detail-container">
                    <s-loading
                        class="detail-container-loading"
                        s-if="detailLoading"
                        loading
                        size="large"
                    ></s-loading>
                    <template s-else>
                        <cluster-not-found s-if="showEmpty" on-back="onBackList" />
                        <template s-else>
                            <div
                                class="name-space-select {{active}}"
                                s-if="titleData.namespace.show"
                            >
                                <label class="name-space-title">命名空间：</label>
                                <s-select
                                    width="230"
                                    datasource="{{titleData.namespace.datasource}}"
                                    value="{=titleData.namespace.value=}"
                                    on-change="onChange"
                                    searchable
                                />
                            </div>
                            <cce-detail-wrap
                                s-if="active === 'detail'"
                                clusterUuid="{{clusterUuid}}"
                                cluster-detail="{{clusterDetail}}"
                                isNewBec="{{isNewBec}}"
                            >
                                <div slot="detail">
                                    <cce-cluster-detail
                                        cluster-detail="{{clusterDetail}}"
                                        clusterDetailV2="{{clusterDetailV2}}"
                                        IPversion="{{IPversion}}"
                                        dcc-uuid="{{dccUuid}}"
                                        on-fresh="onfresh"
                                        loading="{{loading}}"
                                        isNewBec="{{isNewBec}}"
                                        clusterScaleInfo="{{clusterScaleInfo}}"
                                    />
                                </div>
                            </cce-detail-wrap>

                            <cce-instance-list
                                s-if="!isClusterV2 && active === 'instance'"
                                cluster-detail="{{clusterDetail}}"
                                clusterUuid="{{clusterUuid}}"
                                query="{{route.query}}"
                            />

                            <cce-worker-list
                                s-if="isClusterV2 && active === 'instance' && !isNodeLabel"
                                cluster-detail="{{clusterDetail}}"
                                clusterUuid="{{clusterUuid}}"
                                clusterName="{{clusterName}}"
                                query="{{route.query}}"
                                multipleSearch
                                IPversion="{{IPversion}}"
                                isNewBec="{{isNewBec}}"
                            />

                            <cce-master-list
                                s-if="active === 'master'"
                                cluster-detail="{{clusterDetail}}"
                                clusterUuid="{{clusterUuid}}"
                                query="{{route.query}}"
                                IPversion="{{IPversion}}"
                                isNewBec="{{isNewBec}}"
                            />

                            <cce-node-label-list
                                s-if="isNodeLabel"
                                clusterUuid="{{clusterUuid}}"
                                clusterType="{{clusterDetail.clusterType}}"
                            />

                            <cce-group-wrap
                                s-if="active === 'group'"
                                clusterUuid="{{clusterUuid}}"
                                cluster-detail="{{clusterDetail}}"
                                clusterScaleInfo="{{clusterScaleInfo}}"
                            />

                            <cce-vk-list
                                s-if="active === 'vk'"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-name="{{clusterName}}"
                            />

                            <cce-node-pools-list
                                s-if="active === 'nodeGroups'"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-detail="{{clusterDetail}}"
                                route="{{route}}"
                            />

                            <cce-automation-access-list
                                s-if="active === 'access'"
                                clusterUuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <cce-cluster-events
                                s-if="active === 'events'"
                                clusterUuid="{{clusterUuid}}"
                            />

                            <x-namespace-list
                                s-if="active === 'namespace'"
                                cluster-detail="{{clusterDetail}}"
                                clusterName="{{clusterName}}"
                                clusterUuid="{{clusterUuid}}"
                            />

                            <cce-cluster-auto-scale-detail
                                s-if="active === 'autoscale'"
                                clusterUuid="{{clusterUuid}}"
                            />

                            <cce-cluster-report
                                s-if="active === 'clusterReport'"
                                clusterUuid="{{clusterUuid}}"
                            />

                            <s-loading
                                s-if="{{active === 'audit' && auditLoading}}"
                                loading="{{true}}"
                                class="audit-wrap-loading"
                            ></s-loading>

                            <cce-cluster-audit
                                s-if="{{active === 'audit' && clusterAudit === 'old' && !auditLoading}}"
                                clusterUuid="{{clusterUuid}}"
                                on-changeAudit="changeAudit($event)"
                                on-changeOpen="changeOpen($event)"
                                hasDeploy="{{hasDeploy}}"
                                cluster-detail="{{clusterDetail}}"
                                masterType="{{masterType}}"
                            />
                            <cce-audit-log-open
                                s-if="{{active === 'audit' && clusterAudit !== 'old' && !auditLoading}}"
                                clusterUuid="{{clusterUuid}}"
                                clusterAudit="{{clusterAudit}}"
                                on-updateSuccess="updateSuccess($event)"
                                hasDeploy="{{hasDeploy}}"
                                on-changeOpen="changeOpen($event)"
                                isError="{{isError}}"
                                on-checkOpen="checkIfDeployedAudit($event)"
                                auditUpdate="{{auditUpdate}}"
                            />

                            <!--边缘集群-->
                            <cce-cluster-deployment-list
                                s-if="active === 'deployment' && isCloudEdge"
                                cluster-detail="{{clusterDetail}}"
                                isCloudEdge="{{isCloudEdge}}"
                                cluster-uuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                                cluster-name="{{clusterName}}"
                            />

                            <cce-cluster-daemonset-list
                                s-if="active === 'daemonset' && isCloudEdge"
                                cluster-detail="{{clusterDetail}}"
                                isCloudEdge="{{isCloudEdge}}"
                                cluster-uuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                                cluster-name="{{clusterName}}"
                            />

                            <cce-cluster-statefulset-list
                                s-if="active === 'statefulset' && isCloudEdge"
                                cluster-detail="{{clusterDetail}}"
                                isCloudEdge="{{isCloudEdge}}"
                                cluster-uuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                                cluster-name="{{clusterName}}"
                            />

                            <cce-cluster-pod-list
                                s-if="active === 'pod'"
                                cluster-detail="{{clusterDetail}}"
                                isCloudEdge="{{isCloudEdge}}"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-name="{{clusterName}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <cce-cluster-job-list
                                s-if="active === 'job' && isCloudEdge "
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                                cluster-name="{{clusterName}}"
                            />

                            <cce-cluster-cronjob-list
                                s-if="active === 'cronjob' && isCloudEdge"
                                cluster-detail="{{clusterDetail}}"
                                isCloudEdge="{{isCloudEdge}}"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-name="{{clusterName}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <!--普通集群-->
                            <cce-cluster-deployment2-list
                                s-if="active === 'daemonset' && !isCloudEdge"
                                moduleName="daemonset"
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                                cluster-name="{{clusterName}}"
                            />

                            <cce-cluster-deployment2-list
                                s-if="active === 'statefulset' && !isCloudEdge"
                                moduleName="statefulset"
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                                cluster-name="{{clusterName}}"
                            />

                            <cce-cluster-deployment2-list
                                s-if="active === 'deployment' && !isCloudEdge"
                                moduleName="deployment"
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                                cluster-name="{{clusterName}}"
                            />

                            <cce-cluster-job2-list
                                s-if="active === 'job' && !isCloudEdge "
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                                cluster-name="{{clusterName}}"
                            />

                            <cce-cluster-cronjob2-list
                                s-if="active === 'cronjob' && !isCloudEdge"
                                cluster-detail="{{clusterDetail}}"
                                isCloudEdge="{{isCloudEdge}}"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-name="{{clusterName}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <cce-cluster-hpa-list
                                s-if="active === 'hpa'"
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                isNewBec="{{isNewBec}}"
                                cluster-name="{{clusterName}}"
                                namespaceDatasource="{{titleData.namespace.datasource}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <cce-flow-service-list
                                s-if="active === 'flowService'"
                                cluster-detail="{{clusterDetail}}"
                                clusterUuid="{{clusterUuid}}"
                                clusterName="{{clusterName}}"
                                namespaceName="{{titleData.namespace.value}}"
                                namespace="{=titleData.namespace.value=}"
                                namespaceList="{{titleData.namespace.datasource}}"
                                on-namespaceChange="onChange"
                            />

                            <cce-flow-ingress-list
                                s-if="active === 'flowIngress'"
                                clusterName="{{clusterName}}"
                                clusterUuid="{{clusterUuid}}"
                                namespaceName="{{titleData.namespace.value}}"
                                cluster-detail="{{clusterDetail}}"
                            />

                            <cce-cluster-configmap-list
                                s-if="active === 'configmap'"
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-name="{{clusterName}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <cce-cluster-secret-list
                                s-if="active === 'secret'"
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-name="{{clusterName}}"
                                namespace="{=titleData.namespace.value=}"
                                namespaceList="{{titleData.namespace.datasource}}"
                                on-namespaceChange="onChange"
                            />

                            <cce-cluster-storage-class-list
                                s-if="active === 'storageclass'"
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-name="{{clusterName}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <cce-cluster-pv-list
                                s-if="active === 'pv'"
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-name="{{clusterName}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <cce-cluster-pvc-list
                                s-if="active === 'pvc'"
                                cluster-detail="{{clusterDetail}}"
                                cluster-uuid="{{clusterUuid}}"
                                cluster-name="{{clusterName}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <cce-monitor-container
                                s-if="active === 'monitorContainer'"
                                clusterUuid="{{clusterUuid}}"
                                clusterName="{{clusterName}}"
                                clusterDetail="{=clusterDetail=}"
                                query="{{route.query}}"
                            />

                            <x-cluster-monitor
                                s-if="active === 'monitorCluster'"
                                clusterUuid="{{clusterUuid}}"
                                clusterName="{{clusterName}}"
                            />

                            <cce-event-center
                                s-if="active === 'monitorPortrait'"
                                clusterUuid="{{clusterUuid}}"
                            />

                            <cce-batch-deployments-list
                                s-if="active === 'batchDeployments'"
                                clusterUuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                                clusterName="{{clusterName}}"
                            />

                            <cce-ai-aijob-list
                                s-if="active === 'aijob'"
                                clusterUuid="{{clusterUuid}}"
                                namespace="{{titleData.namespace.value}}"
                            />

                            <cce-ai-dataset-list
                                s-if="active === 'dataset'"
                                clusterUuid="{{clusterUuid}}"
                                namespace="{{namespaceName}}"
                                clusterName="{{clusterName}}"
                            />

                            <cce-ai-aiqueue-list
                                s-if="active === 'aiqueue'"
                                clusterUuid="{{clusterUuid}}"
                            />

                            <cce-helm-components
                                s-if="active === 'aihelm' && clusterDetail"
                                clusterUuid="{{clusterUuid}}"
                                on-changeBackCompStatus="changeBackCompStatus"
                                masterType="{{clusterDetail.masterType}}"
                                clusterType="{{clusterDetail.clusterType}}"
                                cluster-detail="{{clusterDetail}}"
                                clusterDetailV2="{{clusterDetailV2}}"
                                route="{{route}}"
                            />

                            <cce-cluster-monitor-log-center
                                s-if="active === 'monitorLogCenter'"
                                clusterUuid="{{clusterUuid}}"
                                cluster-detail="{{clusterDetail}}"
                                clusterDetailV2="{{clusterDetailV2}}"
                                route="{{route}}"
                            />

                            <cce-resource-obj
                                s-if="active === 'resourceObj'"
                                clusterUuid="{{clusterUuid}}"
                            />

                            <cce-back-manger
                                s-if="active === 'backup'"
                                clusterUuid="{{clusterUuid}}"
                                backUpHasInstall="{{backUpHasInstall}}"
                                on-changeBackCompStatus="changeBackCompStatus"
                            />

                            <cce-inspec
                                s-if="active === 'inspec'"
                                clusterUuid="{{clusterUuid}}"
                                query="{=route.query=}"
                            />

                            <cluster-check
                                s-if="active === 'clusterCheck'"
                                clusterUuid="{{clusterUuid}}"
                                cluster-detail="{{clusterDetail}}"
                                query="{=route.query=}"
                            />

                            <cluster-diag
                                s-if="active === 'diag'"
                                clusterUuid="{{clusterUuid}}"
                                cluster-detail="{{clusterDetail}}"
                                query="{=route.query=}"
                            />
                        </template>
                    </template>
                </div>
            </div>
        </s-app-detail-page>
    `;
    static components = {
        'cce-detail-wrap': DetailWrap,
        'cce-cluster-deployment-list': DeploymentList,
        'cce-cluster-pod-list': PodList,
        'cce-cluster-job-list': JobList,
        'cce-cluster-cronjob-list': CronJobList,
        'cce-cluster-daemonset-list': DaemonsetList,
        'cce-cluster-statefulset-list': StatefulsetList,
        'cce-cluster-hpa-list': HpaList,
        'x-namespace-list': NamespaceList,
        'x-cluster-monitor': ClusterMonitor,
        'cce-cluster-configmap-list': ConfigMapList,
        'cce-cluster-secret-list': SecretList,
        'cce-cluster-storage-class-list': StorageClassList,
        'cce-cluster-pv-list': PvList,
        'cce-cluster-pvc-list': PvcList,
        'cce-flow-ingress-list': IngressList,
        'cce-node-pools-list': NodePools,
        'cce-ai-aijob-list': AiJobList,
        'cce-ai-aiqueue-list': AiQueueList,
        'cce-ai-dataset-list': AiDatasetList,
        'cce-cluster-deployment2-list': DeploymentList2,
        'cce-cluster-job2-list': JobList2,
        'cce-cluster-cronjob2-list': CronJobList2,
        'cce-resource-obj': ResourceObj,
        'cce-flow-service-list': FlowServiceList,
        'cce-inspec': Inspec,
        'cluster-check': ClusterCheck,
        'cluster-diag': ClusterDiag,
        'cce-group-wrap': CceGroupWrap,
        's-app-detail-page': AppDetailPage,
        's-sidebar': AppSidebar,
        's-sidebar-item': AppSidebar.Item,
        'cce-back-manger': BackUpList,
        's-loading': Loading,
        's-select': Select,
        's-icon-import': OutlinedImport,
    };

    static filters = {
        getUrlByName(name) {
            const clusterUuid = this.data.get('clusterUuid');
            const namespaceName = this.data.get('titleData.namespace.value');
            const masterType = this.data.get('masterType');
            const params = {clusterUuid, namespaceName, masterType};
            if (name === 'diag') {
                params.active = getQueryByName('active');
            }
            let url = linkConfig(name, params);
            return url;
        },
    };

    static messages = {
        reloadDetail() {
            this.getAllClusterDetail();
        },
        updataNamespace({value}) {
            this.data.set('titleData.namespace.value', value);
            this.data.set('namespaceName', value);
            State.setState('namespaceName', value);
        },
    };

    static computed = {
        isAscendCluster() {
            const clusterDetailV2 = this.data.get('clusterDetailV2');
            return checkAscendCluster(clusterDetailV2);
        },
        isManaged() {
            const clusterDetailV2 = this.data.get('clusterDetailV2');
            const type = clusterDetailV2?.spec?.masterConfig?.masterType;
            return type === MasterType.MANAGEDPRO || type === 'managed';
        },
        blsDisabled() {
            const region = window?.$context?.getCurrentRegionId();
            if (region === 'nj' || region === 'hkg') {
                return true;
            }
            return false;
        },
    };

    initData() {
        const currentRegion = window?.$context?.getCurrentRegionId();
        const isOldSideBar = localStorage.getItem('cce-cluster-detail-is-old-side-bar') === 'true';
        return {
            isOldSideBar: isOldSideBar,
            $flag,
            isBJRegion: currentRegion === 'bj',
            enableInspecRegion: !['hkg', 'nj', 'edge'].includes(currentRegion),
            clusterDetailV2: {},
            titleData: {
                backto: '/cce/cluster/list',
                title: '',
                status: {
                    data: '',
                    statusGroup: ClusterStatus,
                },
                namespace: {
                    show: true,
                    datasource: [],
                    value: '',
                },
            },
            ingressWhite: checkWhiteByName(PermissionType.getTextFromValue(PermissionType.INGRESS)),
            containerWhite: checkWhiteByName(
                PermissionType.getTextFromValue(PermissionType.CONTAINERMONITOR),
            ),
            logWhite: checkWhiteByName(PermissionType.getTextFromValue(PermissionType.LOG)),
            auditWhite: checkWhiteByName(PermissionType.getTextFromValue(PermissionType.AUDIT)),
            loading: false,
            hasLogManage: false,
            clusterAudit: 'new',
            hasDeploy: false,
            isError: false,
            masterType: '',
            auditLoading: true,
            backUpHasInstall: false,
            showEmpty: false,
            detailLoading: false,
        };
    }

    inited() {
        this.setIsNewBec();
        // 设置集群ID 详情页顶部title
        const {clusterUuid, dccUuid, namespaceName, clusterName, masterType} =
            this.data.get('route.query');
        if (!clusterUuid) {
            this.data.set('showEmpty', true);
            return;
        }
        this.data.set('clusterUuid', _.escape(clusterUuid));
        this.data.set('clusterName', _.escape(clusterName));
        this.data.set('namespaceName', _.escape(namespaceName) || '');
        this.data.set('dccUuid', _.escape(dccUuid));
        this.data.set('titleData.title', _.escape(clusterUuid));
        this.data.set('titleData.namespace.value', namespaceName ? _.escape(namespaceName) : '');
        const isClusterV2 = checkClusterV2(clusterUuid);
        this.data.set('isClusterV2', isClusterV2);
        this.data.set('masterType', masterType || store.getState('masterType'));

        State.setState('clusterUuid', _.escape(clusterUuid));
        State.setState('namespaceName', _.escape(namespaceName) || '');

        this.updateActive();

        this.getAllClusterDetail();

        // 显示/隐藏命名空间
        // this.toggleNamespace();

        this.watch('route', () => this.updateActive());
        this.watch('masterType', () => this.getClusterFlavor());

        this.watch('active', item => {
            const clusterUuid = this.data.get('clusterUuid');
            const namespaceName = this.data.get('titleData.namespace.value');
            const masterType = this.data.get('masterType');
            const params = {clusterUuid, namespaceName, masterType};
            if (item === 'diag') {
                params.active = getQueryByName('active');
            }
            let url = linkConfig(item, params);
            this.data.set('isNodeLabel', false);
            redirect(url, {silent: true});
            this.nextTick(() => this.toggleNamespace());
        });
    }

    toggleSideBarVersion() {
        try {
            const isOldSideBar = !this.data.get('isOldSideBar');
            this.data.set('isOldSideBar', isOldSideBar);
            localStorage.setItem('cce-cluster-detail-is-old-side-bar', `${isOldSideBar}`);
        } catch (error) {
            console.error(error);
        }
    }

    // 检查备份组件是否安装
    async checkBackComp() {
        try {
            const data = await this.$http.getCompsData(
                this.data.get('clusterUuid'),
                'cce-backup-controller',
            );
            if (data?.result?.items?.[0]?.instance) {
                this.data.set('backUpHasInstall', true);
            }
        } catch (err) {
            console.error(err);
        }
    }

    changeBackCompStatus(value = true) {
        this.data.set('backUpHasInstall', value);
    }

    // 检查是否开通集群审计功能
    async checkIfDeployedAudit() {
        const region = window?.$context?.getCurrentRegionId();
        try {
            this.data.set('auditLoading', true);
            const res = await this.$http.checkIfDeployedAudit({
                clusterUuid: this.data.get('clusterUuid'),
            });
            this.data.set('auditLoading', false);

            this.data.set('clusterAudit', res?.result?.version || 'old');
            if (region === 'nj' || region === 'hkg') {
                this.data.set('clusterAudit', 'old');
            }
            this.data.set(
                'hasDeploy',
                res?.result === true ? res.result : res?.result?.hasDeploy || false,
            );
            this.data.set('isError', false);
        } catch (e) {
            this.data.set('auditLoading', false);
            this.data.set('isError', true);
        }
    }

    async getAllClusterDetail() {
        const {clusterUuid} = this.data.get('route.query');
        const isClusterV2 = checkClusterV2(clusterUuid);
        const isNewBec = this.data.get('isNewBec');
        this.getClusterFlavor();
        try {
            this.data.set('detailLoading', true);
            if (isClusterV2) {
                await this.loadClusterDetailV2();
            } else {
                await this.loadClusterDetail();
            }
            this.toggleNamespace();
            this.data.set('detailLoading', false);
            this.initDetailClickEvent();
            if (!isNewBec) {
                this.checkIfDeployedAudit();
            }
            if (isClusterV2 && !isNewBec) {
                this.checkBackComp();
            }
        } catch (err) {
            this.data.set('detailLoading', false);
            this.initDetailClickEvent();
            console.error(err);
        }
    }

    initDetailClickEvent() {
        this.nextTick(() => {
            const links = document.querySelectorAll('.name-link');
            // 背景：统一用插槽给详情的title加了a标签，导致点击事件冒泡到a标签上，页面刷新
            // 看着只有disabled的item会有问题，其他的a标签跳转和redirct是一样的，不会刷页面，统一拦截下吧
            links.forEach(link => {
                link.addEventListener('click', function (event) {
                    event.preventDefault();
                });
            });
        });
    }

    async getClusterFlavor() {
        const masterType = this.data.get('masterType');
        if ([MasterType.MANAGEDPRO, 'managed', 'managedOld'].includes(masterType)) {
            const clusterUuid = this.data.get('clusterUuid');
            const {result} = await this.$http.getClusterFlavor(clusterUuid, {
                'x-silent-codes': ['ClusterNotFound'],
            });
            store.dispatch('setMasterFlavor', result?.clusterScaleInfo?.clusterFlavor);
            this.data.set('clusterScaleInfo', result?.clusterScaleInfo);
        } else {
            store.dispatch('setMasterFlavor', null);
            this.data.set('clusterScaleInfo', null);
        }
    }

    onBackList() {
        redirect('#/cce/cluster/list');
    }

    changeAudit() {
        this.data.set('clusterAudit', 'new');
        this.data.set('auditUpdate', true);
    }

    updateSuccess() {
        this.data.set('auditUpdate', false);
        this.data.set('hasDeploy', true);
    }

    changeOpen(value) {
        this.data.set('hasDeploy', value);
    }

    setIsNewBec() {
        // 是否边缘节点
        const region = window.$context.getCurrentRegionId();
        const isNewBec = region === BEC_REGION;
        this.data.set('isNewBec', isNewBec);
        store.dispatch('setIsNewBec', isNewBec);
        return isNewBec;
    }

    updateActive() {
        // 设置侧边栏active
        let path = this.data.get('route.path');
        const active = getActive(path);
        if (active) {
            this.data.set('active', active);
        }
        this.data.set('isNodeLabel', path === '/cce/node-label/list');
    }

    // 刷新集群信息
    onfresh() {
        const clusterUuid = this.data.get('clusterUuid');
        const isClusterV2 = checkClusterV2(clusterUuid);
        this.data.set('loading', true);
        this.getClusterFlavor();
        if (isClusterV2) {
            this.loadClusterDetailV2().then(() => this.data.set('loading', false));
        } else {
            this.loadClusterDetail().then(() => this.data.set('loading', false));
        }
    }

    // 显示/隐藏命名空间
    toggleNamespace() {
        const active = this.data.get('active');
        const showNamespaceActives = [];
        Object.keys(ACTIVES).forEach(key => {
            if (
                ACTIVES[key]?.param?.includes('namespaceName') &&
                !['secret', 'hpa', 'flowService'].includes(key)
            ) {
                showNamespaceActives.push(key);
            }
        });
        const showNamespace = showNamespaceActives.includes(active);
        this.data.set('titleData.namespace.show', showNamespace);
        if (!this.data.get('showEmpty')) {
            this.listAppNamespace();
        }
    }

    // 获取集群详细信息
    async loadClusterDetail() {
        const clusterUuid = this.data.get('clusterUuid');
        try {
            this.data.set('showEmpty', false);
            const result = await this.$http.getClusterDetail(clusterUuid, {
                'x-silent-codes': ['ClusterNotExist'],
            });
            this.initV1Detail(result);
            return Promise.resolve();
        } catch (e) {
            if (e?.code === 'ClusterNotExist') {
                // 集群不存在，展示空页面
                this.data.set('showEmpty', true);
            }
            return Promise.reject();
        } finally {
            setTimeout(() => {
                //自动滚动到当前菜单位置
                const activeSideItem = document.querySelector('.s-app-sidebar-item-active');
                if (activeSideItem) {
                    activeSideItem.scrollIntoView({behavior: 'smooth', block: 'center'});
                }
            }, 200);
        }
    }

    initV1Detail(result) {
        this.data.set('masterType', result?.masterType);
        this.data.set('clusterDetail', result);
        this.data.set('clusterName', result.clusterName);
        this.data.set('titleData.status.data', result.status);
        this.data.set('titleData.title', result.clusterUuid + '（' + result.clusterName + '）');
        this.data.set('isServerless', result.clusterType === ClusterType.SERVERLESS);
        this.data.set('isCloudEdge', result.clusterType === ClusterType.CLOUD_EDGE);
        this.data.set('isARM', result.clusterType === ClusterType.ARM);
        this.data.set('k8sVersion', result.version);
        this.data.set('IPversion', result.ipVersion === 'dualStack');
        store.dispatch('onExtendClusterUuid', result.clusterUuid);
        store.dispatch('changeIpVersion', result.ipVersion === 'dualStack');
        store.dispatch('changeK8sVersion', result.version); // 集群版本
        store.dispatch('setDetailVpcId', result.vpcId);
        let cniMode = result.advancedOptions.cniMode;
        if (cniMode === 'vpc-eni') {
            cniMode = NetworkMode.CNI;
        }
        store.dispatch('setNetworkMode', cniMode);
        this.featureGuide();
    }

    async loadClusterDetailV2() {
        const clusterUuid = this.data.get('clusterUuid');
        try {
            this.data.set('showEmpty', false);
            const result = await this.$http.getClusterDetailV2(clusterUuid, {
                'x-silent-codes': ['ClusterNotFound'],
            });
            this.initV1Detail(clusterDetailTransformV2ToV1(result));
            this.data.set('clusterDetailV2', result?.result?.cluster);
            return Promise.resolve();
        } catch (e) {
            if (e?.code === 'ClusterNotFound') {
                // 集群不存在，展示空页面
                this.data.set('showEmpty', true);
            }
            return Promise.reject();
        }
    }

    featureGuide() {
        // 集群详情页功能引导，只能出现一个功能引导，仅新版左侧导航展示引导
        const {isOldSideBar, isClusterV2, isServerless, isCloudEdge, isNewBec, isARM} =
            this.data.get();
        if (!isOldSideBar) {
            // 等待菜单栏渲染完毕后再进行引导
            setTimeout(() => {
                // 集群巡检(已下线)
                // inspecGuide(() => {
                //     this.data.set('active', 'inspec');
                // });
                // 新版菜单功能引导(已下线)
                // if (isClusterV2 && !isServerless && !isCloudEdge && !isNewBec && !isARM) {
                //     newSideBar(() => {
                //         this.data.set('expandByFeatureGuide', true);
                //     });
                // }
            }, 500);
        }
    }

    onChange(e) {
        this.data.set('namespaceName', e.value);

        State.setState('namespaceName', e.value);
    }

    // 获取空间列表
    async listAppNamespace() {
        const {clusterUuid, namespaceName} = this.data.get();
        try {
            const options = {clusterUuid};
            const {result} = await this.$http.listAppNamespace(options);
            let namespaceList = _.uniq(
                _.map(_.get(result, 'namespaces', []), item => _.get(item, 'objectMeta.name', '')),
            );
            namespaceList = _.map(namespaceList, item => ({
                text: item,
                value: item,
                title: item,
            }));
            namespaceList.unshift({text: '全部命名空间', value: 'all'});
            this.data.set('titleData.namespace.datasource', namespaceList);
            this.data.set(
                'titleData.namespace.value',
                _.find(namespaceList, item => item.value === namespaceName) ? namespaceName : 'all',
            );
            this.data.set('namespaceName', this.data.get('titleData.namespace.value'));

            State.setState('namespaceName', this.data.get('namespaceName'));
        } catch (e) {
            this.data.set('titleData.namespace.value', 'all');
            this.data.set('namespaceName', 'all');
        }
    }
}
