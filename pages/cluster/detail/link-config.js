/**
 * 集群详情页链接配置
 *
 * @file link-config.js
 * <AUTHOR>
 */

import _ from 'lodash';

export const actives = {
    detail: {
        path: '#/cce/cluster/detail',
        param: ['clusterUuid', 'masterType'],
    },
    instance: {
        path: ['#/cce/instance/list', '#/cce/node-label/list'],
        param: ['clusterUuid'],
    },
    master: {
        path: '#/cce/master/list',
        param: ['clusterUuid'],
    },
    group: {
        path: '#/cce/cluster/group',
        param: ['clusterUuid'],
    },
    vk: {
        path: '#/cce/cluster/vk',
        param: ['clusterUuid'],
    },
    events: {
        path: '#/cce/cluster/events',
        param: ['clusterUuid'],
    },
    autoscale: {
        path: '#/cce/cluster/autoscale',
        param: ['clusterUuid'],
    },
    namespace: {
        path: '#/cce/application/namespace/list',
        param: ['clusterUuid', 'clusterName'],
    },
    audit: {
        path: '#/cce/cluster/audit',
        param: ['clusterUuid'],
    },
    flowService: {
        path: '#/cce/flow/service/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    clusterReport: {
        path: '#/cce/cluster/report',
        param: ['clusterUuid'],
    },
    flowIngress: {
        path: '#/cce/flow/ingress/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    deployment: {
        path: '#/cce/application/deployment/list',
        param: ['clusterUuid', 'namespaceName', 'clusterName'],
    },
    pod: {
        path: '#/cce/application/pod/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    cronjob: {
        path: '#/cce/application/cronjob/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    job: {
        path: '#/cce/application/job/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    daemonset: {
        path: '#/cce/application/daemonset/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    statefulset: {
        path: '#/cce/application/statefulset/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    hpa: {
        path: '#/cce/application/hpa/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    monitorCluster: {
        path: '#/cce/monitor/cluster',
        param: ['clusterUuid', 'clusterName'],
    },
    monitorContainer: {
        path: '#/cce/monitor/container',
        param: ['clusterUuid', 'clusterName'],
    },
    monitorPortrait: {
        path: '#/cce/monitor/portrait',
        param: ['clusterUuid'],
    },
    configmap: {
        path: '#/cce/application/configmap/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    secret: {
        path: '#/cce/application/secret/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    storageclass: {
        path: '#/cce/application/storageclass/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    pvc: {
        path: '#/cce/application/pvc/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    pv: {
        path: '#/cce/application/pv/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    nodeGroups: {
        path: '#/cce/nodepools/list',
        param: ['clusterUuid'],
    },
    batchDeployments: {
        path: '#/cce/application/batch-deployments/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    access: {
        path: '#/cce/automation/access/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    aijob: {
        path: '#/cce/ai/aijob/list',
        param: ['clusterUuid', 'namespaceName'],
    },
    aiqueue: {
        path: '#/cce/ai/aiqueue/list',
        param: ['clusterUuid'],
    },
    aihelm: {
        path: '#/cce/ai/helm/release',
        param: ['clusterUuid'],
    },
    dataset: {
        path: '#/cce/ai/dataset/list',
        param: ['clusterUuid', 'namespaceName', 'clusterName'],
    },
    monitorLogCenter: {
        path: '#/cce/monitor/log/center',
        param: ['clusterUuid'],
    },
    resourceObj: {
        path: '#/cce/cluster/resource',
        param: ['clusterUuid'],
    },
    backup: {
        path: '#/cce/cluster/backup',
        param: ['clusterUuid'],
    },
    restore: {
        path: '#/cce/cluster/restore',
        param: ['clusterUuid'],
    },
    inspec: {
        path: '#/cce/cluster/inspec',
        param: ['clusterUuid'],
    },
    clusterCheck: {
        path: '#/cce/cluster/check',
        param: ['clusterUuid'],
    },
    diag: {
        path: '#/cce/cluster/diag',
        param: ['clusterUuid', 'active'],
    },
    role: {
        path: '#/cce/cluster/rbac-role',
        param: ['clusterUuid'],
    },
};

export const linkConfig = (name, param) => {
    const info = actives[name];
    const params =
        (info && _.map(info.param, item => item + '=' + _.get(param, `${item}`, ''))) || [];
    const path = info && (_.isArray(info.path) ? info.path[0] : info.path);
    return (info && path + '?' + params.join('&')) || '';
};

export const getActive = str => {
    let value = '';
    _.each(actives, (d, key) => {
        if (_.includes(d.path, `#${str}`)) {
            value = key;
        }
    });
    return value;
};
