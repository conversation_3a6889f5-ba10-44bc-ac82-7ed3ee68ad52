# RBAC 角色管理功能实现总结

## 功能概述

本功能实现了 CCE 控制台的 RBAC 角色管理模块，支持 ClusterRole 和 Role 两种 Kubernetes RBAC 资源的全生命周期管理。

## 已实现功能

### 1. 主页面和导航

-   ✅ 创建了主页面 `pages/rbac-role/index.js`，支持 ClusterRole 和 Role 的 Tab 页切换
-   ✅ 在侧边栏添加了"角色"导航项，路径为 `/cce/cluster/rbac-role`
-   ✅ 支持通过 URL 参数 `active` 控制默认显示的 Tab 页

### 2. ClusterRole 功能

-   ✅ **列表页面**: 支持搜索、分页、系统默认角色标识
-   ✅ **详情页面**: 展示基本信息、标签、注解和权限规则
-   ✅ **YAML 操作**: 创建、编辑、查看 YAML 配置
-   ✅ **删除功能**: 支持删除确认，系统默认角色受保护
-   ✅ **权限控制**: 系统默认角色只能查看，不能编辑或删除

### 3. Role 功能

-   ✅ **列表页面**: 支持命名空间筛选、搜索、分页
-   ✅ **详情页面**: 展示基本信息（含命名空间）和权限规则
-   ✅ **YAML 操作**: 创建、编辑、查看 YAML 配置
-   ✅ **删除功能**: 支持删除确认，包含命名空间信息
-   ✅ **命名空间支持**: 完整的命名空间级别权限管理

### 4. 技术实现

-   ✅ **API 接口**: 在 `utils/client.js` 中添加了完整的 RBAC API 接口
-   ✅ **组件复用**: YAML 弹框组件支持 ClusterRole 和 Role 两种资源类型
-   ✅ **样式设计**: 统一的 UI 风格，符合现有设计规范
-   ✅ **错误处理**: 完善的异常处理和用户提示

## 文件结构

```
pages/rbac-role/
├── index.js                    # 主页面入口，Tab页切换
├── style.less                  # 主页面样式
├── cluster-role/
│   ├── list/
│   │   ├── list.js             # ClusterRole列表组件
│   │   ├── schema.js           # 列表配置
│   │   ├── field.js            # 数据处理
│   │   ├── delete-cluster-role.js  # 删除确认弹框
│   │   └── style.less          # 样式文件
│   └── detail/
│       ├── index.js            # ClusterRole详情页
│       ├── detail.js           # 详情组件
│       └── field.js            # 详情数据处理
├── role/
│   ├── list/
│   │   ├── list.js             # Role列表组件
│   │   ├── schema.js           # 列表配置
│   │   ├── field.js            # 数据处理
│   │   ├── delete-role.js      # 删除确认弹框
│   │   └── style.less          # 样式文件
│   └── detail/
│       ├── index.js            # Role详情页
│       ├── detail.js           # 详情组件
│       └── field.js            # 详情数据处理
└── components/
    ├── yaml-create-dialog.js   # YAML创建弹框
    ├── yaml-edit-dialog.js     # YAML编辑弹框
    └── yaml-view-dialog.js     # YAML查看弹框
```

## API 接口

已在 `utils/client.js` 中添加以下 API 接口，使用标准的 CRD API 格式：

### ClusterRole 接口

-   `getClusterRoleList(params)` - 获取 ClusterRole 列表
    -   URL: `/api/cce/app/crd/rbac.authorization.k8s.io/v1/ClusterRole/`
-   `getClusterRoleDetail(params)` - 获取 ClusterRole 详情
    -   URL: `/api/cce/app/crd/rbac.authorization.k8s.io/v1/ClusterRole/`
-   `getClusterRoleYaml(params)` - 获取 ClusterRole YAML
    -   URL: `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/ClusterRole/name/{name}`
-   `createClusterRole(params)` - 创建 ClusterRole
    -   URL: `/api/cce/app/appdeploymentfromfile`
-   `updateClusterRole(params)` - 更新 ClusterRole
    -   URL: `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/ClusterRole/name/{name}`
-   `deleteClusterRole(params)` - 删除 ClusterRole
    -   URL: `/api/cce/app/_raw/ClusterRole`

### Role 接口

-   `getRoleList(params)` - 获取 Role 列表
    -   URL: `/api/cce/app/crd/rbac.authorization.k8s.io/v1/Role/{namespace}`
-   `getRoleDetail(params)` - 获取 Role 详情
    -   URL: `/api/cce/app/crd/rbac.authorization.k8s.io/v1/Role/{namespace}`
-   `getRoleYaml(params)` - 获取 Role YAML
    -   URL: `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/Role/namespace/{namespace}/name/{name}`
-   `createRole(params)` - 创建 Role
    -   URL: `/api/cce/app/appdeploymentfromfile`
-   `updateRole(params)` - 更新 Role
    -   URL: `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/Role/namespace/{namespace}/name/{name}`
-   `deleteRole(params)` - 删除 Role
    -   URL: `/api/cce/app/_raw/Role`

### API 特点

-   使用标准的 Kubernetes RBAC API 组：`rbac.authorization.k8s.io/v1`
-   ClusterRole 是集群级别资源，与命名空间无关
-   Role 是命名空间级别资源，需要指定命名空间
-   支持分页、搜索、过滤等功能

## 使用说明

1. **访问页面**: 在 CCE 控制台侧边栏"安全管理"分组下点击"角色"进入角色管理页面
2. **切换资源类型**: 使用 Tab 页在 ClusterRole 和 Role 之间切换
3. **列表操作**: 支持搜索、分页、查看详情、YAML 操作、删除
4. **创建资源**: 点击"YAML 创建"按钮，编辑 YAML 配置创建新资源
5. **编辑资源**: 对于非系统默认角色，可以点击"编辑 YAML"修改配置
6. **查看详情**: 点击资源名称进入详情页，查看完整信息

## 注意事项

1. **系统默认角色保护**: 以 `system:`、`cluster-admin`、`admin`、`edit`、`view` 开头的角色被识别为系统默认角色，只能查看不能修改
2. **命名空间权限**: Role 资源需要指定命名空间，确保有相应的命名空间访问权限
3. **YAML 格式**: 创建和编辑时会自动验证 YAML 格式，确保配置正确性
4. **权限验证**: 后端需要实现相应的权限校验，防止越权操作

## 后续优化建议

1. **批量操作**: 支持批量删除、批量导出等功能
2. **模板功能**: 提供常用角色模板，简化创建流程
3. **可视化编辑**: 提供图形化的权限配置界面
4. **操作审计**: 记录所有角色变更操作日志
5. **权限分析**: 提供权限使用情况分析和建议
