# RBAC API接口修复说明

## 修复内容

根据您的反馈，我已经将RBAC相关的API接口修改为使用标准的CRD API格式，确保与现有系统的API模式保持一致。

## 修复的API接口

### 1. ClusterRole接口修复

**修复前**：
```javascript
// 使用自定义的API路径
getClusterRoleList(params) {
    return this.get(`/api/v1/clusters/${params.clusterUuid}/clusterroles`, {...});
}
```

**修复后**：
```javascript
// 使用标准的CRD API路径
getClusterRoleList(params) {
    const {clusterUuid, pageNo, pageSize, keyword} = params;
    let url = `/api/cce/app/crd/rbac.authorization.k8s.io/v1/ClusterRole/?clusterUuid=${clusterUuid}&page=${pageNo}&itemsPerPage=${pageSize}`;
    if (keyword) {
        url += `&filterBy=name,${keyword}`;
    }
    return this.get(url);
}
```

### 2. Role接口修复

**修复前**：
```javascript
// 使用自定义的API路径
getRoleList(params) {
    return this.get(`/api/v1/clusters/${params.clusterUuid}/namespaces/${params.namespace}/roles`, {...});
}
```

**修复后**：
```javascript
// 使用标准的CRD API路径，包含命名空间
getRoleList(params) {
    const {clusterUuid, namespace, pageNo, pageSize, keyword} = params;
    let url = `/api/cce/app/crd/rbac.authorization.k8s.io/v1/Role/${namespace}?clusterUuid=${clusterUuid}&page=${pageNo}&itemsPerPage=${pageSize}`;
    if (keyword) {
        url += `&filterBy=name,${keyword}`;
    }
    return this.get(url);
}
```

## API规范说明

### 1. API组和版本
- **Group**: `rbac.authorization.k8s.io`
- **Version**: `v1`
- **Kind**: `ClusterRole` 或 `Role`

### 2. URL模式

#### ClusterRole（集群级别资源）
- **列表**: `/api/cce/app/crd/rbac.authorization.k8s.io/v1/ClusterRole/`
- **详情**: `/api/cce/app/crd/rbac.authorization.k8s.io/v1/ClusterRole/?filterBy=name,{name}`
- **YAML**: `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/ClusterRole/name/{name}`
- **创建**: `/api/cce/app/appdeploymentfromfile`
- **更新**: `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/ClusterRole/name/{name}`
- **删除**: `/api/cce/app/_raw/ClusterRole`

#### Role（命名空间级别资源）
- **列表**: `/api/cce/app/crd/rbac.authorization.k8s.io/v1/Role/{namespace}`
- **详情**: `/api/cce/app/crd/rbac.authorization.k8s.io/v1/Role/{namespace}?filterBy=name,{name}`
- **YAML**: `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/Role/namespace/{namespace}/name/{name}`
- **创建**: `/api/cce/app/appdeploymentfromfile`
- **更新**: `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/Role/namespace/{namespace}/name/{name}`
- **删除**: `/api/cce/app/_raw/Role`

### 3. 参数说明

#### 查询参数
- `clusterUuid`: 集群UUID（必需）
- `page`: 页码（分页）
- `itemsPerPage`: 每页条数（分页）
- `filterBy`: 过滤条件，格式为 `name,{keyword}`

#### 删除参数
```javascript
{
    group: 'rbac.authorization.k8s.io',
    version: 'v1',
    kind: 'ClusterRole' | 'Role',
    name: '资源名称',
    namespace: '命名空间', // 仅Role需要
    clusterUuid: '集群UUID'
}
```

## 数据处理修复

### 1. 响应格式适配
修复了数据处理函数以支持不同的API响应格式：
- `result.items` 或 `result.clusterRoles/roles`
- `result.listMeta.totalItems` 或 `result.totalCount`
- `item.metadata` 或 `item.objectMeta`

### 2. YAML处理
修复了YAML获取和处理逻辑：
- 支持 `result.yaml`、`result.data` 或直接返回对象
- 自动转换对象为YAML字符串格式

## 兼容性保证

1. **向后兼容**: 数据处理函数支持多种响应格式
2. **错误处理**: 完善的异常处理和降级逻辑
3. **类型安全**: 使用可选链操作符避免空值错误

## 测试建议

1. **API连通性测试**: 确认所有API接口能正常调用
2. **数据格式测试**: 验证返回数据的格式和字段
3. **分页功能测试**: 测试列表的分页和搜索功能
4. **YAML操作测试**: 验证YAML的获取、编辑和保存功能
5. **权限测试**: 确认系统默认角色的保护机制

## 注意事项

1. **命名空间处理**: Role资源必须指定命名空间，ClusterRole无需命名空间
2. **权限控制**: 确保后端实现了相应的权限校验
3. **系统角色保护**: 前端已实现系统默认角色的保护逻辑
4. **错误提示**: 提供友好的错误信息和用户提示
