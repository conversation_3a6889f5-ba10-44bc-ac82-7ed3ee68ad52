# RBAC角色管理功能测试指南

## 测试前准备

1. **确保后端API接口已实现**：
   - ClusterRole相关接口：`/api/v1/clusters/{clusterUuid}/clusterroles`
   - Role相关接口：`/api/v1/clusters/{clusterUuid}/namespaces/{namespace}/roles`

2. **确保有测试集群和命名空间**：
   - 至少一个可用的CCE集群
   - 集群中有可用的命名空间

## 功能测试清单

### 1. 导航和页面访问测试
- [ ] 在CCE控制台侧边栏能看到"角色"导航项
- [ ] 点击"角色"能正确跳转到角色管理页面
- [ ] 页面显示ClusterRole和Role两个Tab页
- [ ] Tab页切换功能正常

### 2. ClusterRole功能测试

#### 2.1 列表页面测试
- [ ] ClusterRole列表能正常加载
- [ ] 搜索功能正常（按名称搜索）
- [ ] 分页功能正常
- [ ] 刷新功能正常
- [ ] 系统默认角色显示"系统默认角色"标签
- [ ] 创建时间显示正确

#### 2.2 YAML操作测试
- [ ] 点击"YAML创建"按钮能打开创建弹框
- [ ] YAML编辑器功能正常
- [ ] 创建成功后列表自动刷新
- [ ] 对于普通角色，"编辑YAML"按钮可用
- [ ] 对于系统默认角色，只显示"查看YAML"按钮
- [ ] YAML编辑和查看功能正常

#### 2.3 删除功能测试
- [ ] 普通角色的删除按钮可用
- [ ] 系统默认角色的删除按钮被禁用
- [ ] 删除确认弹框显示正确信息
- [ ] 删除成功后列表自动刷新

#### 2.4 详情页面测试
- [ ] 点击ClusterRole名称能跳转到详情页
- [ ] 详情页显示基本信息（名称、创建时间、UUID）
- [ ] 标签和注解正确显示
- [ ] 权限规则表格正确显示
- [ ] 返回按钮功能正常
- [ ] 刷新按钮功能正常

### 3. Role功能测试

#### 3.1 列表页面测试
- [ ] Role列表能正常加载
- [ ] 命名空间下拉选择器功能正常
- [ ] 切换命名空间后列表自动刷新
- [ ] 搜索功能正常（按名称搜索）
- [ ] 分页功能正常
- [ ] 命名空间列显示正确

#### 3.2 YAML操作测试
- [ ] 点击"YAML创建"按钮能打开创建弹框
- [ ] 默认YAML模板包含当前选择的命名空间
- [ ] 创建成功后列表自动刷新
- [ ] 编辑和查看YAML功能正常

#### 3.3 删除功能测试
- [ ] 删除确认弹框显示Role名称和命名空间
- [ ] 删除成功后列表自动刷新

#### 3.4 详情页面测试
- [ ] 点击Role名称能跳转到详情页
- [ ] 详情页显示基本信息（包含命名空间）
- [ ] 权限规则表格正确显示
- [ ] 返回链接包含正确的命名空间参数

### 4. 错误处理测试
- [ ] 网络错误时显示适当的错误信息
- [ ] YAML格式错误时显示验证错误
- [ ] 权限不足时显示相应提示
- [ ] 资源不存在时显示404错误

### 5. 边界情况测试
- [ ] 空列表状态显示正常
- [ ] 长名称的资源显示正常（文本截断）
- [ ] 大量权限规则的显示性能
- [ ] 特殊字符在名称中的处理

## 性能测试

1. **列表加载性能**：
   - 测试大量ClusterRole/Role时的加载速度
   - 分页性能测试

2. **YAML编辑器性能**：
   - 大型YAML文件的编辑响应速度
   - 语法高亮和验证性能

## 兼容性测试

1. **浏览器兼容性**：
   - Chrome、Firefox、Safari、Edge
   - 不同屏幕分辨率下的显示效果

2. **Kubernetes版本兼容性**：
   - 测试不同版本的Kubernetes集群
   - RBAC API版本兼容性

## 安全测试

1. **权限验证**：
   - 确保用户只能操作有权限的资源
   - 系统默认角色的保护机制

2. **输入验证**：
   - YAML内容的安全性验证
   - 防止XSS攻击

## 测试数据准备

### ClusterRole测试数据
```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: test-cluster-role
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
```

### Role测试数据
```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: test-role
  namespace: default
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
```

## 问题报告模板

发现问题时，请按以下格式报告：

**问题描述**：
**重现步骤**：
1. 
2. 
3. 

**期望结果**：
**实际结果**：
**浏览器/环境**：
**截图**：（如适用）
