# 角色管理导航配置最终说明

## 实现要求

✅ **在集群内的安全管理导航栏位置下新增角色模块**
✅ **单击进入角色管理页面**  
✅ **支持用户管理 K8s 资源 ClusterRole 和 Role 资源**
✅ **默认为 ClusterRole Tab 页**
✅ **位于集群审计上方**

## 导航结构

### CCE控制台主侧边栏（保持不变）
```
├── 概览
├── 集群管理
│   ├── 集群列表
│   ├── 权限管理        ← 保持原位置
│   └── 集群快照
├── Helm
└── ...
```

### 集群详情页面侧边栏（新增角色）
```
├── 集群详情
├── 节点管理
├── ...
├── 安全管理
│   ├── 角色            ← 新增位置
│   └── 集群审计        ← 原有位置
└── ...
```

## 修改的文件

1. **`pages/cluster/detail/index.js`**
   - 在安全管理分组中添加角色导航项
   - 位置：集群审计上方
   - 路由名称：`rbac-role`

## 功能特性

### 1. 访问路径
- **位置**：集群详情页 → 安全管理 → 角色
- **URL**：`#/cce/cluster/rbac-role?clusterUuid={clusterUuid}`

### 2. 页面功能
- **默认Tab**：ClusterRole（集群角色）
- **Tab切换**：支持切换到Role（命名空间角色）
- **完整CRUD**：创建、查看、编辑、删除操作

### 3. 资源管理
- **ClusterRole**：集群级别角色，无命名空间限制
- **Role**：命名空间级别角色，需选择命名空间

## 权限管理说明

- **权限管理**保持在CCE控制台主侧边栏的集群管理分组下
- **角色管理**位于集群详情页面的安全管理分组下
- 两者功能独立，互不影响

## 用户使用流程

1. 登录CCE控制台
2. 进入集群列表，选择具体集群
3. 在集群详情页面左侧导航栏找到"安全管理"
4. 点击"角色"进入角色管理页面
5. 默认显示ClusterRole列表
6. 可通过Tab切换查看Role列表

## 技术实现

### 导航配置代码
```javascript
<s-sidebar-item title="角色" name="rbac-role">
    <div slot="title">
        <a class="name-link" href="{{'rbac-role'| getUrlByName}}">角色</a>
    </div>
</s-sidebar-item>
```

### 可见性控制
- 安全管理分组：`visible="{{!isNewBec}}"`
- 角色导航项：无额外限制，始终可见

## 注意事项

1. **集群上下文**：角色管理功能在集群详情页面内，自动获取当前集群上下文
2. **权限控制**：用户需要有相应的集群访问权限
3. **系统角色保护**：系统默认角色受保护，只能查看不能修改
4. **命名空间权限**：Role操作需要相应的命名空间权限
