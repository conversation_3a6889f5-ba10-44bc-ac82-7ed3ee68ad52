/**
 * @file pages/rbac-role/role/list/list.js
 * <AUTHOR> Assistant
 * @description Role列表页面组件
 */

import {html} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import listPage from '../../../../common/list/list-page';
import asTable from '../../../../common/list/table';

import './style.less';
import schema from './schema';
import {getData} from './field';
import YamlCreateDialog from '../../components/yaml-create-dialog';
import YamlEditDialog from '../../components/yaml-edit-dialog';
import YamlViewDialog from '../../components/yaml-view-dialog';
import DeleteRole from './delete-role';

const tableSlot = html`
    <div slot="c-operation">
        <s-button skin="stringfy" on-click="viewYaml(row, rowIndex)" s-if="row.isSystemDefault">
            查看YAML
        </s-button>
        <s-button skin="stringfy" on-click="editYaml(row, rowIndex)" s-if="!row.isSystemDefault">
            编辑YAML
        </s-button>
        <s-button
            skin="stringfy"
            on-click="deleteRole(row, rowIndex)"
            disabled="{{row.isSystemDefault}}"
            title="{{row.isSystemDefault ? '系统默认角色不支持删除' : ''}}"
        >
            删除
        </s-button>
    </div>
`;

const Table = asTable(tableSlot);

export default class RoleList extends listPage(schema, Table) {
    initData() {
        const data = super.initData();
        return {
            ...data,
        };
    }

    // YAML创建
    onYamlCreate() {
        const dialog = new YamlCreateDialog({
            data: {
                title: '创建Role',
                resourceType: 'Role',
                defaultYaml: this.getDefaultRoleYaml(),
                clusterUuid: this.data.get('clusterUuid'),
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.refreshTable();
        });
    }

    // 查看YAML
    viewYaml(row) {
        const dialog = new YamlViewDialog({
            data: {
                title: `查看Role - ${row.name}`,
                clusterUuid: this.data.get('clusterUuid'),
                name: row.name,
                namespace: row.namespace,
                resourceType: 'Role',
            },
        });
        dialog.attach(document.body);
    }

    // 编辑YAML
    editYaml(row) {
        const dialog = new YamlEditDialog({
            data: {
                title: `编辑Role - ${row.name}`,
                clusterUuid: this.data.get('clusterUuid'),
                name: row.name,
                namespace: row.namespace,
                resourceType: 'Role',
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.refreshTable();
        });
    }

    // 删除Role
    deleteRole(row) {
        const dialog = new DeleteRole({
            data: {
                clusterUuid: this.data.get('clusterUuid'),
                name: row.name,
                namespace: row.namespace,
                creationTimestamp: row.creationTimestamp,
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.refreshTable();
        });
    }

    // 获取默认Role YAML模板
    getDefaultRoleYaml() {
        const namespace = this.data.get('namespace') || 'default';
        return `apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: role-example
  namespace: ${namespace}
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]`;
    }

    // 命名空间变化处理
    onNamespaceChange(namespace) {
        this.data.set('namespace', namespace);
        this.refreshTable();
    }

    // 数据请求
    listRequester(payload) {
        const {namespace, clusterUuid} = this.data.get();
        if (!clusterUuid || !namespace) {
            return Promise.resolve({
                pageSize: 20,
                totalCount: 0,
                result: [],
            });
        }

        if (payload.keyword) {
            payload.keyword = payload.keyword.trim();
            payload.pageNo = 1;
        }

        return this.$http
            .getRoleList({...payload, clusterUuid, namespace})
            .then(response => {
                // 处理不同的响应格式
                const result = response.result || response;
                const list = getData(result) || {};
                return {
                    pageSize: list.pageSize || 20,
                    totalCount: list.totalCount || 0,
                    result: list.result || [],
                };
            })
            .catch(e => {
                return {
                    pageSize: 20,
                    totalCount: 0,
                    result: [],
                };
            });
    }
}
