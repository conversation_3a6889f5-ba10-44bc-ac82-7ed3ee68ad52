/**
 * @file pages/rbac-role/role/list/schema.js
 * <AUTHOR> Assistant
 * @description Role列表页面Schema配置
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {utcToTime} from '../../../../utils/util';

export default {
    $title: 'Role',
    $withSearchbox: true,
    $withPager: true,
    $withRefresh: true,
    toolbar: [
        {
            type: 'button',
            label: 'YAML创建',
            skin: 'create',
            actionType: 'custom',
            action() {
                this.onYamlCreate();
            },
        },
    ],
    rightToolbarTpl: html`<s-select
        value="{=namespace=}"
        width="200"
        searchable
        datasource="{{namespaceDatasource}}"
        on-change="onNamespaceChange"
    >
    </s-select>`,
    body: {
        api: 'listRequester',
        filter: {
            placeholder: '请输入Role名称进行搜索',
            width: 200,
            $searchbox: {
                keywordType: 'name',
            },
            keywordTypes: [{text: 'Role名称', value: 'name'}],
        },
        $extraPayload: {
            manner: 'page',
        },
        columns: [
            {
                name: 'name',
                label: '名称',
                width: 200,
                render(item) {
                    const {clusterUuid} = this.data.get();
                    return (
                        '<a class="link-item" href="#/cce/cluster/rbac-role/role/detail?' +
                        `clusterUuid=${clusterUuid}&name=${item.name}&namespace=${
                            item.namespace
                        }">${_.escape(item.name)}</a>`
                    );
                },
                fixed: 'left',
            },
            {
                name: 'namespace',
                label: '命名空间',
                width: 150,
            },
            {
                name: 'creationTimestamp',
                label: '创建时间',
                width: 180,
                render(item) {
                    return utcToTime(item.creationTimestamp);
                },
            },
            {
                name: 'roleType',
                label: '角色类型',
                width: 120,
                render(item) {
                    if (item.isSystemDefault) {
                        return '<span class="system-default-tag">系统默认角色</span>';
                    }
                    return '普通角色';
                },
            },
            {
                name: 'operation',
                label: '操作',
                width: 200,
                fixed: 'right',
            },
        ],
    },
};
