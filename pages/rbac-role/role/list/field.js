/**
 * @file pages/rbac-role/role/list/field.js
 * <AUTHOR> Assistant
 * @description Role列表数据处理函数
 */

/**
 * 处理Role列表数据
 * @param {Object} result - API返回的原始数据
 * @returns {Object} 处理后的列表数据
 */
export function getData(result) {
    const resultList = [];
    if (result?.items) {
        for (const item of result.items) {
            resultList.push({
                name: item.metadata.name,
                namespace: item.metadata.namespace,
                creationTimestamp: item.metadata.creationTimestamp,
                isSystemDefault: isSystemDefaultRole(item),
                rules: item.rules || [],
                labels: formatLabels(item.metadata.labels),
                annotations: formatAnnotations(item.metadata.annotations),
                uuid: item.metadata.uid,
                item,
            });
        }
    }
    return {
        totalCount: result.totalCount,
        pageNo: result.pageNo,
        pageSize: result.pageSize,
        result: resultList,
    };
}

/**
 * 判断是否为系统默认角色
 * @param {Object} item - Role对象
 * @returns {boolean} 是否为系统默认角色
 */
export function isSystemDefaultRole(item) {
    // 根据实际业务逻辑判断，可能通过labels、annotations或名称前缀等
    const systemRoles = ['system:', 'cluster-admin', 'admin', 'edit', 'view'];
    return systemRoles.some(prefix => item.metadata.name.startsWith(prefix));
}

/**
 * 格式化标签
 * @param {Object} labels - 标签对象
 * @returns {Array} 格式化后的标签数组
 */
export function formatLabels(labels) {
    if (!labels) return [];
    return Object.entries(labels).map(([name, value]) => ({
        name,
        value,
    }));
}

/**
 * 格式化注解
 * @param {Object} annotations - 注解对象
 * @returns {Array} 格式化后的注解数组
 */
export function formatAnnotations(annotations) {
    if (!annotations) return [];
    return Object.entries(annotations).map(([name, value]) => ({
        name,
        value,
    }));
}
