/**
 * @file pages/rbac-role/role/detail/detail.js
 * <AUTHOR> Assistant
 * @description Role详情组件
 */

import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Button, Table} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {utcToTime} from '../../../../utils/util';
import YamlEditDialog from '../../components/yaml-edit-dialog';
import YamlViewDialog from '../../components/yaml-view-dialog';

const {asComponent, invokeComp, invokeBceSanUI} = decorators;

const template = html`
    <template>
        <div class="role-detail-actions">
            <s-button on-click="editYaml" s-if="!detail.isSystemDefault"> 编辑YAML </s-button>
            <s-button on-click="viewYaml" s-if="detail.isSystemDefault"> 查看YAML </s-button>
            <s-button class="button-refresh" on-click="refresh">
                <s-icon-refresh is-button="{{false}}" />
            </s-button>
        </div>

        <app-legend label="基本信息">
            <app-detail-cell datasource="{{baseInfo}}" divide="{{2}}">
                <template slot="c-labels">
                    <span s-if="!item.value || item.value.length === 0">-</span>
                    <div s-else class="labels-container">
                        <span s-for="label in item.value" class="label-item">
                            {{label.name}}={{label.value}}
                        </span>
                    </div>
                </template>
                <template slot="c-annotations">
                    <span s-if="!item.value || item.value.length === 0">-</span>
                    <div s-else class="annotations-container">
                        <span s-for="annotation in item.value" class="annotation-item">
                            {{annotation.name}}={{annotation.value}}
                        </span>
                    </div>
                </template>
                <template slot="c-createAt">{{item.value | getTime}}</template>
            </app-detail-cell>
        </app-legend>

        <app-legend label="规则">
            <s-table
                columns="{{rulesTable.columns}}"
                datasource="{{rulesTable.datasource}}"
                loading="{{loading}}"
            >
                <template slot="c-resources">
                    <span s-if="!row.resources || row.resources.length === 0">-</span>
                    <span s-else>{{row.resources.join(', ')}}</span>
                </template>
                <template slot="c-verbs">
                    <span s-if="!row.verbs || row.verbs.length === 0">-</span>
                    <span s-else>{{row.verbs.join(', ')}}</span>
                </template>
                <template slot="c-apiGroups">
                    <span s-if="!row.apiGroups || row.apiGroups.length === 0">-</span>
                    <span s-else>{{row.apiGroups.join(', ')}}</span>
                </template>
                <template slot="c-resourceNames">
                    <span s-if="!row.resourceNames || row.resourceNames.length === 0">-</span>
                    <span s-else>{{row.resourceNames.join(', ')}}</span>
                </template>
                <template slot="c-nonResourceURLs">
                    <span s-if="!row.nonResourceURLs || row.nonResourceURLs.length === 0">-</span>
                    <span s-else>{{row.nonResourceURLs.join(', ')}}</span>
                </template>
            </s-table>
        </app-legend>
    </template>
`;

@asComponent('@role-detail')
@invokeComp('@app-legend', '@app-detail-cell')
@invokeBceSanUI
class RoleDetail extends Component {
    static template = template;

    static components = {
        's-button': Button,
        's-table': Table,
        's-icon-refresh': OutlinedRefresh,
        'app-legend': AppLegend,
    };

    static computed = {
        baseInfo() {
            const detail = this.data.get('detail');
            if (!detail) return [];

            return [
                {
                    name: 'name',
                    label: '名称',
                    value: detail.name,
                },
                {
                    name: 'namespace',
                    label: '命名空间',
                    value: detail.namespace,
                },
                {
                    name: 'createAt',
                    label: '创建时间',
                    value: detail.creationTimestamp,
                },
                {
                    name: 'uuid',
                    label: 'UUID',
                    value: detail.uuid,
                },
                {
                    name: 'labels',
                    label: '标签',
                    value: detail.labels,
                },
                {
                    name: 'annotations',
                    label: '注解',
                    value: detail.annotations,
                },
            ];
        },
        rulesTable() {
            const detail = this.data.get('detail');
            return {
                columns: [
                    {
                        name: 'resources',
                        label: '资源',
                        width: 200,
                    },
                    {
                        name: 'verbs',
                        label: '动作',
                        width: 200,
                    },
                    {
                        name: 'nonResourceURLs',
                        label: '非资源URL',
                        width: 200,
                    },
                    {
                        name: 'resourceNames',
                        label: '资源名',
                        width: 200,
                    },
                    {
                        name: 'apiGroups',
                        label: 'API组',
                        width: 200,
                    },
                ],
                datasource: detail?.rules || [],
            };
        },
    };

    static filters = {
        getTime(value) {
            return value ? utcToTime(value) : '-';
        },
    };

    editYaml() {
        const {detail, clusterUuid} = this.data.get();
        const dialog = new YamlEditDialog({
            data: {
                title: `编辑Role - ${detail.name}`,
                clusterUuid,
                name: detail.name,
                namespace: detail.namespace,
                resourceType: 'Role',
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.fire('refresh');
        });
    }

    viewYaml() {
        const {detail, clusterUuid} = this.data.get();
        const dialog = new YamlViewDialog({
            data: {
                title: `查看Role - ${detail.name}`,
                clusterUuid,
                name: detail.name,
                namespace: detail.namespace,
                resourceType: 'Role',
            },
        });
        dialog.attach(document.body);
    }

    refresh() {
        this.fire('refresh');
    }
}

export default RoleDetail;
