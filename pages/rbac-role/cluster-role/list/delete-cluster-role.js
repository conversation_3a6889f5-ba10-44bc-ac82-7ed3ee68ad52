/**
 * @file pages/rbac-role/cluster-role/list/delete-cluster-role.js
 * <AUTHOR> Assistant
 * @description ClusterRole删除确认弹框
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Table, Notification} from '@baidu/sui';
import {OutlinedWarning} from '@baidu/sui-icon';
import {utcToTime} from '../../../../utils/util';

const template = html`
    <template>
        <s-dialog
            open="{=open=}"
            title="删除ClusterRole"
            class="delete-cluster-role-dialog"
            on-confirm="onConfirm"
            on-close="onClose"
            confirming="{{confirming}}"
        >
            <div class="delete-tip">
                <s-icon-warning class="warning-icon" />
                <div class="message">
                    <p>删除后无法恢复！</p>
                    <p>请确认删除ClusterRole</p>
                </div>
            </div>

            <s-table
                columns="{{table.columns}}"
                datasource="{{table.datasource}}"
                class="delete-info-table"
            >
            </s-table>
        </s-dialog>
    </template>
`;

export default class DeleteClusterRole extends Component {
    static template = template;

    static components = {
        's-dialog': Dialog,
        's-table': Table,
        's-icon-warning': OutlinedWarning,
    };

    initData() {
        return {
            open: false,
            confirming: false,
            table: {
                columns: [
                    {name: 'name', label: 'ClusterRole名称'},
                    {name: 'creationTimestamp', label: '创建时间'},
                ],
                datasource: [],
            },
        };
    }

    attached() {
        const {name, creationTimestamp} = this.data.get();
        this.data.set('table.datasource', [
            {
                name,
                creationTimestamp: utcToTime(creationTimestamp),
            },
        ]);
        this.data.set('open', true);
    }

    onClose() {
        this.data.set('open', false);
        this.dispose();
    }

    async onConfirm() {
        const {clusterUuid, name} = this.data.get();

        try {
            this.data.set('confirming', true);

            await this.$http.deleteClusterRole({
                clusterUuid,
                name,
            });

            Notification.success('删除成功');
            this.fire('confirm');
            this.onClose();
        } catch (e) {
            Notification.error(e.message || '删除失败');
        } finally {
            this.data.set('confirming', false);
        }
    }
}
