/**
 * @file pages/rbac-role/cluster-role/list/list.js
 * <AUTHOR> Assistant
 * @description ClusterRole列表页面组件
 */

import {html} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import listPage from '../../../../common/list/list-page';
import asTable from '../../../../common/list/table';

import './style.less';
import schema from './schema';
import {getData} from './field';
import YamlCreateDialog from '../../components/yaml-create-dialog';
import YamlEditDialog from '../../components/yaml-edit-dialog';
import YamlViewDialog from '../../components/yaml-view-dialog';
import DeleteClusterRole from './delete-cluster-role';

const tableSlot = html`
    <div slot="c-operation">
        <s-button skin="stringfy" on-click="viewYaml(row, rowIndex)" s-if="row.isSystemDefault">
            查看YAML
        </s-button>
        <s-button skin="stringfy" on-click="editYaml(row, rowIndex)" s-if="!row.isSystemDefault">
            编辑YAML
        </s-button>
        <s-button
            skin="stringfy"
            on-click="deleteClusterRole(row, rowIndex)"
            disabled="{{row.isSystemDefault}}"
            title="{{row.isSystemDefault ? '系统默认角色不支持删除' : ''}}"
        >
            删除
        </s-button>
    </div>
`;

const Table = asTable(tableSlot);

export default class ClusterRoleList extends listPage(schema, Table) {
    initData() {
        const data = super.initData();
        return {
            ...data,
        };
    }

    // YAML创建
    onYamlCreate() {
        const dialog = new YamlCreateDialog({
            data: {
                title: '创建ClusterRole',
                resourceType: 'ClusterRole',
                defaultYaml: this.getDefaultClusterRoleYaml(),
                clusterUuid: this.data.get('clusterUuid'),
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.refreshTable();
        });
    }

    // 查看YAML
    viewYaml(row) {
        const dialog = new YamlViewDialog({
            data: {
                title: `查看ClusterRole - ${row.name}`,
                clusterUuid: this.data.get('clusterUuid'),
                name: row.name,
                resourceType: 'ClusterRole',
            },
        });
        dialog.attach(document.body);
    }

    // 编辑YAML
    editYaml(row) {
        const dialog = new YamlEditDialog({
            data: {
                title: `编辑ClusterRole - ${row.name}`,
                clusterUuid: this.data.get('clusterUuid'),
                name: row.name,
                resourceType: 'ClusterRole',
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.refreshTable();
        });
    }

    // 删除ClusterRole
    deleteClusterRole(row) {
        const dialog = new DeleteClusterRole({
            data: {
                clusterUuid: this.data.get('clusterUuid'),
                name: row.name,
                creationTimestamp: row.creationTimestamp,
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.refreshTable();
        });
    }

    // 获取默认ClusterRole YAML模板
    getDefaultClusterRoleYaml() {
        return `apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: clusterRole-example
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]`;
    }

    // 数据请求
    listRequester(payload) {
        const {clusterUuid} = this.data.get();
        if (!clusterUuid) {
            return Promise.resolve({
                pageSize: 20,
                totalCount: 0,
                result: [],
            });
        }

        if (payload.keyword) {
            payload.keyword = payload.keyword.trim();
            payload.pageNo = 1;
        }

        return this.$http
            .getClusterRoleList({...payload, clusterUuid})
            .then(({result}) => {
                const list = getData(result) || {};
                return {
                    pageSize: result.pageSize || 20,
                    totalCount: result?.totalCount || 0,
                    result: list.result || [],
                };
            })
            .catch(e => {
                return {
                    pageSize: 20,
                    totalCount: 0,
                    result: [],
                };
            });
    }
}
