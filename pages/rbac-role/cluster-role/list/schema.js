/**
 * @file pages/rbac-role/cluster-role/list/schema.js
 * <AUTHOR> Assistant
 * @description ClusterRole列表页面Schema配置
 */

import _ from 'lodash';
import {utcToTime} from '../../../../utils/util';

export default {
    $title: 'ClusterRole',
    $withSearchbox: true,
    $withPager: true,
    $withRefresh: true,
    toolbar: [
        {
            type: 'button',
            label: 'YAML创建',
            skin: 'create',
            actionType: 'custom',
            action() {
                this.onYamlCreate();
            },
        },
    ],
    body: {
        api: 'listRequester',
        filter: {
            placeholder: '请输入ClusterRole名称进行搜索',
            width: 200,
            $searchbox: {
                keywordType: 'name',
            },
            keywordTypes: [{text: 'ClusterRole名称', value: 'name'}],
        },
        $extraPayload: {
            manner: 'page',
        },
        columns: [
            {
                name: 'name',
                label: '名称',
                width: 200,
                render(item) {
                    const {clusterUuid} = this.data.get();
                    return (
                        '<a class="link-item" href="#/cce/cluster/rbac-role/clusterrole/detail?' +
                        `clusterUuid=${clusterUuid}&name=${item.name}">${_.escape(item.name)}</a>`
                    );
                },
                fixed: 'left',
            },
            {
                name: 'creationTimestamp',
                label: '创建时间',
                width: 180,
                render(item) {
                    return utcToTime(item.creationTimestamp);
                },
            },
            {
                name: 'roleType',
                label: '角色类型',
                width: 120,
                render(item) {
                    if (item.isSystemDefault) {
                        return '<span class="system-default-tag">系统默认角色</span>';
                    }
                    return '普通角色';
                },
            },
            {
                name: 'operation',
                label: '操作',
                width: 200,
                fixed: 'right',
            },
        ],
    },
};
