/**
 * @file pages/rbac-role/cluster-role/detail/field.js
 * <AUTHOR> Assistant
 * @description ClusterRole详情页数据处理函数
 */

import {formatLabels, formatAnnotations, isSystemDefaultRole} from '../list/field';

/**
 * 处理ClusterRole详情数据
 * @param {Object} result - API返回的原始数据
 * @returns {Object} 处理后的详情数据
 */
export function getData(result) {
    // 详情API可能返回单个对象或包含items的列表
    let item = result.item || result;

    // 如果返回的是列表格式，取第一个
    if (result.items && result.items.length > 0) {
        item = result.items[0];
    } else if (result.clusterRoles && result.clusterRoles.length > 0) {
        item = result.clusterRoles[0];
    }

    const metadata = item.metadata || item.objectMeta || {};

    return {
        name: metadata.name,
        creationTimestamp: metadata.creationTimestamp,
        uuid: metadata.uid,
        isSystemDefault: isSystemDefaultRole(item),
        labels: formatLabels(metadata.labels),
        annotations: formatAnnotations(metadata.annotations),
        rules: item.rules || [],
    };
}
