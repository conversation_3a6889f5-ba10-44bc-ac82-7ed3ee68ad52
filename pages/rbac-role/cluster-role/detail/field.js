/**
 * @file pages/rbac-role/cluster-role/detail/field.js
 * <AUTHOR> Assistant
 * @description ClusterRole详情页数据处理函数
 */

import {formatLabels, formatAnnotations, isSystemDefaultRole} from '../list/field';

/**
 * 处理ClusterRole详情数据
 * @param {Object} result - API返回的原始数据
 * @returns {Object} 处理后的详情数据
 */
export function getData(result) {
    const item = result.item || result;

    return {
        name: item.metadata.name,
        creationTimestamp: item.metadata.creationTimestamp,
        uuid: item.metadata.uid,
        isSystemDefault: isSystemDefaultRole(item),
        labels: formatLabels(item.metadata.labels),
        annotations: formatAnnotations(item.metadata.annotations),
        rules: item.rules || [],
    };
}
