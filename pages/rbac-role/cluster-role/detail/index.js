/**
 * @file pages/rbac-role/cluster-role/detail/index.js
 * <AUTHOR> Assistant
 * @description ClusterRole详情页面
 */

import {html, decorators, DetailPage} from '@baiducloud/runtime';
import {AppDetailPage} from '@baidu/sui-biz';
import {getData} from './field';

const {invokeAppComp, asPage, invokeBceSanUI, invokeComp} = decorators;

const template = html`
    <app-detail-page>
        <div slot="pageTitle" class="app-page-title">
            <cce-page-title title-data="{=titleData=}" on-fresh="onFresh" />
        </div>
        <div class="cluster-role-detail-content">
            <cluster-role-detail
                detail="{{detail}}"
                cluster-uuid="{{clusterUuid}}"
                cluster-name="{{clusterName}}"
                on-refresh="getDetail"
            />
        </div>
    </app-detail-page>
`;

@asPage('/cce/cluster/rbac-role/clusterrole/detail')
@invokeAppComp
@invokeBceSanUI
@invokeComp('@cce-page-title', '@cluster-role-detail')
class ClusterRoleDetail extends DetailPage {
    static pageName = 'cce-cluster-role-detail';
    REGION_CHANGE_LOCATION = '#/cce/cluster/list';
    static template = template;

    static components = {
        'app-detail-page': AppDetailPage,
    };

    initData() {
        return {
            titleData: {
                backto: '',
                title: '',
                fresh: true,
            },
            detail: {},
        };
    }

    attached() {
        this.setTitleData();
        this.getDetail();
    }

    setTitleData() {
        const {clusterUuid, name} = this.data.get();
        this.data.set(
            'titleData.backto',
            `#/cce/cluster/rbac-role?clusterUuid=${clusterUuid}&active=clusterrole`,
        );
        this.data.set('titleData.title', name);
    }

    async getDetail() {
        const {clusterUuid, name} = this.data.get();
        this.data.set('loading', true);
        try {
            const response = await this.$http.getClusterRoleDetail({
                clusterUuid,
                name,
            });
            // 处理不同的响应格式
            const result = response.result || response;
            const detail = getData(result);
            this.data.set('detail', detail);
            this.data.set('loading', false);
        } catch (e) {
            this.data.set('loading', false);
        }
    }

    onFresh() {
        this.getDetail();
    }
}

export default ClusterRoleDetail;
