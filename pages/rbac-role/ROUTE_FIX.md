# 角色管理路由跳转问题修复

## 问题描述

点击集群详情页面"安全管理"分组下的"角色"导航项后，跳转不符合预期。

## 问题原因

在集群详情页面的侧边栏中，所有导航项的URL都是通过`getUrlByName`过滤器生成的，该过滤器调用`linkConfig`函数来根据路由名称生成对应的URL。

但是在`pages/cluster/detail/link-config.js`文件的`actives`对象中，缺少了`rbac-role`的路由配置，导致点击"角色"时无法生成正确的URL。

## 修复方案

在`pages/cluster/detail/link-config.js`文件中添加`rbac-role`的路由配置：

```javascript
'rbac-role': {
    path: '#/cce/cluster/rbac-role',
    param: ['clusterUuid'],
},
```

## 修复后的效果

1. **正确的URL生成**：点击"角色"导航项时，会生成正确的URL格式：
   ```
   #/cce/cluster/rbac-role?clusterUuid={clusterUuid}
   ```

2. **参数传递**：自动传递当前集群的`clusterUuid`参数，确保角色管理页面能获取到正确的集群上下文。

3. **导航一致性**：与其他集群详情页面的导航项保持一致的行为。

## 技术细节

### URL生成流程

1. **模板中的调用**：
   ```html
   <a class="name-link" href="{{'rbac-role'| getUrlByName}}">角色</a>
   ```

2. **过滤器处理**：
   ```javascript
   getUrlByName(name) {
       const clusterUuid = this.data.get('clusterUuid');
       const params = {clusterUuid, ...};
       let url = linkConfig(name, params);
       return url;
   }
   ```

3. **路由配置查找**：
   ```javascript
   const info = actives[name]; // 查找 'rbac-role' 配置
   ```

4. **URL拼接**：
   ```javascript
   const params = _.map(info.param, item => item + '=' + _.get(param, item, ''));
   return info.path + '?' + params.join('&');
   ```

### 配置说明

- **path**: `#/cce/cluster/rbac-role` - 角色管理页面的路径
- **param**: `['clusterUuid']` - 需要传递的参数列表

## 验证方法

1. 进入任意集群的详情页面
2. 在左侧导航栏找到"安全管理"分组
3. 点击"角色"导航项
4. 验证是否正确跳转到角色管理页面，且URL包含正确的clusterUuid参数

## 相关文件

- **修复文件**: `pages/cluster/detail/link-config.js`
- **导航配置**: `pages/cluster/detail/index.js`
- **目标页面**: `pages/rbac-role/index.js`

## 注意事项

1. **参数一致性**: 确保传递的参数名称与角色管理页面期望接收的参数名称一致
2. **路径正确性**: 确保路径与实际的路由配置匹配
3. **权限控制**: 路由跳转成功后，页面内部仍需要进行相应的权限验证
