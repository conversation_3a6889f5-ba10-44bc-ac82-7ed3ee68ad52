/**
 * @file pages/rbac-role/components/yaml-view-dialog.js
 * <AUTHOR> Assistant
 * @description YAML查看弹框组件
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Notification} from '@baidu/sui';
import YamlEditor from '../../../components/yaml-editor/index';

const template = html`
    <template>
        <s-dialog
            s-ref="dialog"
            open="{=open=}"
            title="{{title}}"
            class="rbac-role-view-yaml"
            width="1000"
            height="600"
            foot="{{false}}"
        >
            <div class="yaml-content">
                <yaml-editor
                    enhanced
                    value="{{yamlContent}}"
                    width="{{950}}"
                    height="{{500}}"
                    readonly="{{true}}"
                />
            </div>

            <div class="dialog-actions">
                <s-button on-click="onClose">关闭</s-button>
            </div>
        </s-dialog>
    </template>
`;

export default class YamlViewDialog extends Component {
    static template = template;

    static components = {
        's-dialog': Dialog,
        's-button': But<PERSON>,
        'yaml-editor': YamlEditor,
    };

    initData() {
        return {
            open: false,
            title: '',
            yamlContent: '',
        };
    }

    async attached() {
        const {clusterUuid, name, resourceType, namespace} = this.data.get();

        try {
            // 获取当前资源的YAML
            let result;
            if (resourceType === 'ClusterRole') {
                result = await this.$http.getClusterRoleYaml({clusterUuid, name});
            } else if (resourceType === 'Role') {
                result = await this.$http.getRoleYaml({clusterUuid, name, namespace});
            }

            this.data.set('yamlContent', result.yaml);
            this.data.set('open', true);
        } catch (e) {
            Notification.error('获取YAML失败');
            this.dispose();
        }
    }

    onClose() {
        this.data.set('open', false);
        this.dispose();
    }
}
