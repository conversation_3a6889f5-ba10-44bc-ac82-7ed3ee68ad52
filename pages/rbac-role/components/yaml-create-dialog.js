/**
 * @file pages/rbac-role/components/yaml-create-dialog.js
 * <AUTHOR> Assistant
 * @description YAML创建弹框组件
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Notification} from '@baidu/sui';
import YamlEditor from '../../../components/yaml-editor/index';
import jsyaml from 'js-yaml';

const template = html`
    <template>
        <s-dialog
            s-ref="dialog"
            open="{=open=}"
            title="{{title}}"
            class="rbac-role-create-yaml"
            width="1000"
            height="600"
        >
            <div class="create-yaml-tip">
                通过YAML方式创建{{resourceType}}资源，请编辑下方配置并提交创建。
            </div>
            <div class="yaml-content">
                <yaml-editor enhanced value="{=yamlContent=}" width="{{950}}" height="{{400}}" />
            </div>

            <div slot="footer">
                <s-button width="46" on-click="onClose">取消</s-button>
                <s-button width="46" skin="primary" on-click="onConfirm" loading="{{confirming}}">
                    创建
                </s-button>
            </div>
        </s-dialog>
    </template>
`;

export default class YamlCreateDialog extends Component {
    static template = template;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        'yaml-editor': YamlEditor,
    };

    initData() {
        return {
            open: false,
            title: '',
            resourceType: '',
            yamlContent: '',
            confirming: false,
        };
    }

    attached() {
        const {defaultYaml} = this.data.get();
        this.data.set('yamlContent', defaultYaml);
        this.data.set('open', true);
    }

    onClose() {
        this.data.set('open', false);
        this.dispose();
    }

    async onConfirm() {
        const {yamlContent, clusterUuid, resourceType} = this.data.get();

        try {
            this.data.set('confirming', true);

            // 验证YAML格式
            const yamlObj = jsyaml.load(yamlContent);

            // 调用API创建资源
            if (resourceType === 'ClusterRole') {
                await this.$http.createClusterRole({
                    clusterUuid,
                    yaml: yamlContent,
                });
            } else if (resourceType === 'Role') {
                await this.$http.createRole({
                    clusterUuid,
                    yaml: yamlContent,
                });
            }

            Notification.success('创建成功');
            this.fire('confirm');
            this.onClose();
        } catch (e) {
            Notification.error(e.message || '创建失败');
        } finally {
            this.data.set('confirming', false);
        }
    }
}
