/**
 * @file pages/rbac-role/components/yaml-edit-dialog.js
 * <AUTHOR> Assistant
 * @description YAML编辑弹框组件
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Notification} from '@baidu/sui';
import YamlEditor from '../../../components/yaml-editor/index';
import jsyaml from 'js-yaml';

const template = html`
    <template>
        <s-dialog
            s-ref="dialog"
            open="{=open=}"
            title="{{title}}"
            class="rbac-role-edit-yaml"
            width="1000"
            height="600"
        >
            <div class="yaml-content">
                <yaml-editor enhanced value="{=yamlContent=}" width="{{950}}" height="{{450}}" />
            </div>

            <div slot="footer">
                <s-button width="46" on-click="onClose">取消</s-button>
                <s-button width="46" skin="primary" on-click="onConfirm" loading="{{confirming}}">
                    更新
                </s-button>
            </div>
        </s-dialog>
    </template>
`;

export default class YamlEditDialog extends Component {
    static template = template;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        'yaml-editor': YamlEditor,
    };

    initData() {
        return {
            open: false,
            title: '',
            yamlContent: '',
            confirming: false,
        };
    }

    async attached() {
        const {clusterUuid, name, resourceType, namespace} = this.data.get();

        try {
            // 获取当前资源的YAML
            let result;
            if (resourceType === 'ClusterRole') {
                result = await this.$http.getClusterRoleYaml({clusterUuid, name});
            } else if (resourceType === 'Role') {
                result = await this.$http.getRoleYaml({clusterUuid, name, namespace});
            }

            // 处理不同的返回格式
            const yamlContent = result.yaml || result.data || result;
            this.data.set(
                'yamlContent',
                typeof yamlContent === 'string'
                    ? yamlContent
                    : JSON.stringify(yamlContent, null, 2),
            );
            this.data.set('open', true);
        } catch (e) {
            Notification.error('获取YAML失败');
            this.dispose();
        }
    }

    onClose() {
        this.data.set('open', false);
        this.dispose();
    }

    async onConfirm() {
        const {yamlContent, clusterUuid, name, resourceType, namespace} = this.data.get();

        try {
            this.data.set('confirming', true);

            // 验证YAML格式
            const yamlObj = jsyaml.load(yamlContent);

            // 调用API更新资源
            if (resourceType === 'ClusterRole') {
                await this.$http.updateClusterRole({
                    clusterUuid,
                    name,
                    yaml: yamlContent,
                });
            } else if (resourceType === 'Role') {
                await this.$http.updateRole({
                    clusterUuid,
                    name,
                    namespace,
                    yaml: yamlContent,
                });
            }

            Notification.success('更新成功');
            this.fire('confirm');
            this.onClose();
        } catch (e) {
            Notification.error(e.message || '更新失败');
        } finally {
            this.data.set('confirming', false);
        }
    }
}
