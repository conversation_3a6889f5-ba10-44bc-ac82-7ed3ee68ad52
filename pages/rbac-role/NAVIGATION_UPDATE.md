# 导航结构调整说明

## 调整内容

根据要求，已将角色管理模块放置在集群详情页面内的"安全管理"分组下，位于集群审计上方。

## 修改前的导航结构

```
CCE控制台主侧边栏
├── 概览
├── 集群管理
│   ├── 集群列表
│   ├── 权限管理
│   └── 集群快照
├── Helm
└── ...

集群详情页面侧边栏
├── 集群详情
├── 节点管理
├── ...
├── 安全管理
│   └── 集群审计
└── ...
```

## 修改后的导航结构

```
CCE控制台主侧边栏
├── 概览
├── 集群管理
│   ├── 集群列表
│   └── 集群快照
├── Helm
└── ...

集群详情页面侧边栏
├── 集群详情
├── 节点管理
├── ...
├── 安全管理
│   ├── 权限管理
│   ├── 角色          ← 新增位置
│   └── 集群审计
└── ...
```

## 功能特性

### 1. 导航位置

-   **位置**: 集群详情页面内的安全管理分组
-   **菜单项**: 角色
-   **路径**: `/cce/cluster/rbac-role`
-   **顺序**: 位于集群审计上方

### 2. 页面默认设置

-   **默认 Tab**: ClusterRole（集群角色）
-   **支持切换**: 可通过 Tab 切换到 Role（命名空间角色）
-   **URL 参数**: 支持通过 `?active=role` 参数直接打开 Role 页面

### 3. 资源管理

-   **ClusterRole**: 集群级别的角色资源，不受命名空间限制
-   **Role**: 命名空间级别的角色资源，需要选择特定命名空间

## 访问方式

1. **通过导航菜单**:

    - 登录 CCE 控制台
    - 在左侧导航栏找到"安全管理"分组
    - 点击"角色"菜单项

2. **直接 URL 访问**:
    - ClusterRole 页面: `#/cce/cluster/rbac-role`
    - Role 页面: `#/cce/cluster/rbac-role?active=role`

## 权限说明

-   用户需要有相应的集群访问权限才能查看和管理角色资源
-   系统默认角色（如 system:开头的角色）受到保护，只能查看不能修改或删除
-   Role 资源的操作需要用户对相应命名空间有访问权限

## 技术实现

### 修改的文件

-   `components/sidebar.js` - 侧边栏导航配置

### 导航配置代码

```javascript
<s-sidebar-item title="安全管理">
    <s-sidebar-item title="权限管理" name="cce-cluster-rbac" link-to="/cce/cluster/rbac" />
    <s-sidebar-item title="角色" name="cce-cluster-rbac-role" link-to="/cce/cluster/rbac-role" />
</s-sidebar-item>
```

## 注意事项

1. **权限继承**: 角色管理功能继承了原有的权限控制逻辑
2. **功能完整性**: 所有 CRUD 操作（创建、读取、更新、删除）都已实现
3. **用户体验**: 保持了与其他 CCE 功能一致的操作体验
4. **安全性**: 系统默认角色受到保护，防止误操作

## 后续扩展

"安全管理"分组为后续安全相关功能提供了扩展空间，可以在此分组下添加：

-   服务账户管理
-   角色绑定管理
-   安全策略配置
-   审计日志查看
-   等其他安全相关功能
