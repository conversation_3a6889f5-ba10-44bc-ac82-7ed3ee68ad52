/**
 * @file pages/rbac-role/index.js
 * <AUTHOR> Assistant
 * @description RBAC角色管理主页面，包含ClusterRole和Role的Tab页切换
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {Tabs} from '@baidu/sui';
import {getQueryByName} from '../../utils/util';

import ClusterRoleList from './cluster-role/list/list';
import RoleList from './role/list/list';

import './style.less';

const template = html`<template>
    <div class="cce-rbac-role-wrap">
        <s-tabs active="{=active=}">
            <s-tabpane key="clusterrole" label="ClusterRole" lazy>
                <cluster-role-list
                    namespaceDatasource="{{namespaceDatasource}}"
                    namespace="{{namespace}}"
                    cluster-detail="{{clusterDetail}}"
                    cluster-uuid="{{clusterUuid}}"
                    cluster-name="{{clusterName}}"
                />
            </s-tabpane>
            <s-tabpane key="role" label="Role" lazy>
                <role-list
                    namespaceDatasource="{{namespaceDatasource}}"
                    namespace="{{namespace}}"
                    cluster-detail="{{clusterDetail}}"
                    cluster-uuid="{{clusterUuid}}"
                    cluster-name="{{clusterName}}"
                />
            </s-tabpane>
        </s-tabs>
    </div>
</template>`;

export default class RbacRole extends Component {
    static template = template;

    static components = {
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        'cluster-role-list': ClusterRoleList,
        'role-list': RoleList,
    };

    initData() {
        return {
            active: 'clusterrole',
        };
    }

    attached() {
        const active = getQueryByName('active');
        if (active === 'role') {
            this.data.set('active', active);
        } else {
            this.data.set('active', 'clusterrole');
        }
    }
}
