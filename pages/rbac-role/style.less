/**
 * @file pages/rbac-role/style.less
 * <AUTHOR> Assistant
 * @description RBAC角色管理页面样式
 */

.cce-rbac-role-wrap {
    .s-tabs {
        .s-tabpane {
            padding: 0;
        }
    }
}

// 系统默认角色标识
.system-default-tag {
    display: inline-block;
    padding: 2px 8px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.2;
}

// 详情页操作按钮
.cluster-role-detail-actions,
.role-detail-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;

    .button-refresh {
        border: none;
        background: none;
        color: #666;

        &:hover {
            color: #144bcc;
        }
    }
}

// 标签和注解容器
.labels-container,
.annotations-container {
    .label-item,
    .annotation-item {
        display: inline-block;
        margin: 2px 4px 2px 0;
        padding: 2px 6px;
        background-color: #f5f5f5;
        border-radius: 3px;
        font-size: 12px;
        color: #333;
    }
}

// 删除确认弹框
.delete-cluster-role-dialog,
.delete-role-dialog {
    .delete-tip {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;

        .warning-icon {
            color: #ff9500;
            margin-right: 8px;
            margin-top: 2px;
        }

        .message {
            p {
                margin: 0 0 4px 0;

                &:first-child {
                    font-weight: 500;
                    color: #ff9500;
                }
            }
        }
    }

    .delete-info-table {
        margin-top: 16px;
    }
}

// YAML弹框样式
.rbac-role-create-yaml,
.rbac-role-edit-yaml,
.rbac-role-view-yaml {
    .create-yaml-tip {
        margin-bottom: 16px;
        padding: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #666;
        font-size: 14px;
    }

    .yaml-content {
        position: relative;
    }

    .dialog-actions {
        display: flex;
        justify-content: flex-end;
        padding: 16px 0 0 0;
        border-top: 1px solid #e8e8e8;
        margin-top: 16px;
    }
}
