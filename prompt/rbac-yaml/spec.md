# 角色管理功能详细设计规格

## 1. 概述

本文档描述了 CCE 控制台新增角色管理功能模块的详细技术设计，包括 ClusterRole 和 Role 资源的全生命周期管理功能。

## 2. 功能架构

### 2.1 页面结构

```
角色管理页面
├── 页面标题：角色
├── Tab页切换
│   ├── ClusterRole Tab页
│   └── Role Tab页
├── 列表功能
│   ├── 搜索筛选
│   ├── 创建操作
│   ├── 列表展示
│   └── 操作按钮（查看/编辑/删除YAML）
└── 详情页
    ├── 基本信息
    ├── 规则信息
    └── 操作按钮
```

### 2.2 技术栈参考

-   Tab 页样式：参考 `pages/hpa/index.js` 中的水平伸缩 Tab 页实现
-   列表组件：参考 `pages/hpa/hpa/list/` 和 `pages/hpa/cronHpa/list/` 的列表实现
-   详情页：参考工作负载详情页结构 `pages/workload/deployment/detail/`
-   YAML 编辑：参考 `components/cluster/workload/edit-yaml.js` 和 `components/yaml-editor/`
-   删除确认：参考 `pages/hpa/hpa/list/delete-hpa.js`

## 3. 文件结构设计

### 3.1 目录结构

```
pages/rbac-role/
├── index.js                    # 主页面入口，包含Tab页切换
├── cluster-role/
│   ├── list/
│   │   ├── index.js            # ClusterRole列表页面
│   │   ├── schema.js           # 列表配置
│   │   ├── list.js             # 列表逻辑
│   │   ├── slot.js             # 列表插槽
│   │   ├── field.js            # 数据处理
│   │   ├── delete-cluster-role.js  # 删除确认弹框
│   │   └── style.less          # 样式文件
│   └── detail/
│       ├── index.js            # ClusterRole详情页
│       ├── detail.js           # 详情组件
│       └── field.js            # 详情数据处理
├── role/
│   ├── list/
│   │   ├── index.js            # Role列表页面
│   │   ├── schema.js           # 列表配置
│   │   ├── list.js             # 列表逻辑
│   │   ├── slot.js             # 列表插槽
│   │   ├── field.js            # 数据处理
│   │   ├── delete-role.js      # 删除确认弹框
│   │   └── style.less          # 样式文件
│   └── detail/
│       ├── index.js            # Role详情页
│       ├── detail.js           # 详情组件
│       └── field.js            # 详情数据处理
└── components/
    ├── yaml-create-dialog.js   # YAML创建弹框
    ├── yaml-edit-dialog.js     # YAML编辑弹框
    └── yaml-view-dialog.js     # YAML查看弹框
```

## 4. 主页面设计 (pages/rbac-role/index.js)

### 4.1 页面模板

参考 `pages/hpa/index.js` 的 Tab 页实现：

```javascript
const template = html`<template>
    <div class="cce-rbac-role-wrap">
        <s-tabs active="{=active=}">
            <s-tabpane key="clusterrole" label="ClusterRole" lazy>
                <cluster-role-list
                    namespaceDatasource="{{namespaceDatasource}}"
                    namespace="{{namespace}}"
                    cluster-detail="{{clusterDetail}}"
                    cluster-uuid="{{clusterUuid}}"
                    cluster-name="{{clusterName}}"
                />
            </s-tabpane>
            <s-tabpane key="role" label="Role" lazy>
                <role-list
                    namespaceDatasource="{{namespaceDatasource}}"
                    namespace="{{namespace}}"
                    cluster-detail="{{clusterDetail}}"
                    cluster-uuid="{{clusterUuid}}"
                    cluster-name="{{clusterName}}"
                />
            </s-tabpane>
        </s-tabs>
    </div>
</template>`;
```

### 4.2 路由配置

-   主页面路由：`/cce/cluster/rbac-role`
-   ClusterRole 详情：`/cce/cluster/rbac-role/clusterrole/detail`
-   Role 详情：`/cce/cluster/rbac-role/role/detail`

### 4.3 导航栏配置

在 `components/sidebar.js` 中的安全管理部分添加：

```javascript
<s-sidebar-item title="角色" name="cce-cluster-rbac-role" link-to="/cce/cluster/rbac-role" />
```

## 5. ClusterRole 列表设计

### 5.1 列表 Schema (pages/rbac-role/cluster-role/list/schema.js)

参考 `pages/hpa/hpa/list/schema.js`：

```javascript
export default {
    $title: 'ClusterRole',
    $withSearchbox: true,
    $withPager: true,
    $withRefresh: true,
    $yamlCreate: 'YAML创建',
    body: {
        api: 'listRequester',
        filter: {
            placeholder: '请输入ClusterRole名称进行搜索',
            width: 200,
            $searchbox: {
                keywordType: 'name',
            },
            keywordTypes: [{text: 'ClusterRole名称', value: 'name'}],
        },
        $extraPayload: {
            manner: 'page',
        },
        columns: [
            {
                name: 'name',
                label: '名称',
                width: 200,
                render: item => {
                    const {clusterUuid} = this.data.get();
                    return (
                        '<a class="link-item" href="#/cce/cluster/rbac-role/clusterrole/detail?' +
                        `clusterUuid=${clusterUuid}&name=${item.name}">${_.escape(item.name)}</a>`
                    );
                },
                fixed: 'left',
            },
            {
                name: 'creationTimestamp',
                label: '创建时间',
                width: 180,
                render: item => utcToTime(item.creationTimestamp),
            },
            {
                name: 'roleType',
                label: '角色类型',
                width: 120,
                render: item => {
                    if (item.isSystemDefault) {
                        return '<span class="system-default-tag">系统默认角色</span>';
                    }
                    return '普通角色';
                },
            },
            {
                name: 'operation',
                label: '操作',
                width: 200,
                fixed: 'right',
            },
        ],
    },
};
```

### 5.2 列表组件 (pages/rbac-role/cluster-role/list/list.js)

参考 `pages/hpa/hpa/list/list.js`：

```javascript
export default class ClusterRoleList extends ListPage {
    static template = getTemplate(
        html`
            <div slot="c-operation">
                <s-button
                    skin="stringfy"
                    on-click="viewYaml(row, rowIndex)"
                    s-if="row.isSystemDefault"
                >
                    查看YAML
                </s-button>
                <s-button
                    skin="stringfy"
                    on-click="editYaml(row, rowIndex)"
                    s-if="!row.isSystemDefault"
                >
                    编辑YAML
                </s-button>
                <s-button
                    skin="stringfy"
                    on-click="deleteClusterRole(row, rowIndex)"
                    disabled="{{row.isSystemDefault}}"
                    title="{{row.isSystemDefault ? '系统默认角色不支持删除' : ''}}"
                >
                    删除
                </s-button>
            </div>
        `,
    );

    // YAML创建
    onYamlCreate() {
        const dialog = new YamlCreateDialog({
            data: {
                title: '创建ClusterRole',
                resourceType: 'ClusterRole',
                defaultYaml: this.getDefaultClusterRoleYaml(),
                clusterUuid: this.data.get('clusterUuid'),
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.refreshTable();
        });
    }

    // 查看YAML
    viewYaml(row) {
        const dialog = new YamlViewDialog({
            data: {
                title: `查看ClusterRole - ${row.name}`,
                clusterUuid: this.data.get('clusterUuid'),
                name: row.name,
                resourceType: 'ClusterRole',
            },
        });
        dialog.attach(document.body);
    }

    // 编辑YAML
    editYaml(row) {
        const dialog = new YamlEditDialog({
            data: {
                title: `编辑ClusterRole - ${row.name}`,
                clusterUuid: this.data.get('clusterUuid'),
                name: row.name,
                resourceType: 'ClusterRole',
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.refreshTable();
        });
    }

    // 删除ClusterRole
    deleteClusterRole(row) {
        const dialog = new DeleteClusterRole({
            data: {
                clusterUuid: this.data.get('clusterUuid'),
                name: row.name,
                creationTimestamp: row.creationTimestamp,
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.refreshTable();
        });
    }

    // 获取默认ClusterRole YAML模板
    getDefaultClusterRoleYaml() {
        return `apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: clusterRole-example
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]`;
    }

    // 数据请求
    doRequest(payload) {
        const {clusterUuid} = this.data.get();
        if (payload.keyword) {
            payload.keyword = payload.keyword.trim();
            payload.pageNo = 1;
        }

        return this.$http
            .getClusterRoleList({...payload, clusterUuid})
            .then(({result}) => {
                const list = this.getData(result) || {};
                return {
                    page: {
                        pageNo: result.pageNo || 1,
                        result: list.result,
                        totalCount: result?.totalCount,
                    },
                };
            })
            .catch(e => {
                return {
                    page: {
                        pageNo: 1,
                        result: [],
                        totalCount: 0,
                    },
                };
            });
    }

    // 数据处理
    getData(result) {
        const resultList = [];
        if (result?.items) {
            for (const item of result.items) {
                resultList.push({
                    name: item.metadata.name,
                    creationTimestamp: item.metadata.creationTimestamp,
                    isSystemDefault: this.isSystemDefaultRole(item),
                    rules: item.rules || [],
                    item,
                });
            }
        }
        return {
            totalCount: result.totalCount,
            pageNo: result.pageNo,
            pageSize: result.pageSize,
            result: resultList,
        };
    }

    // 判断是否为系统默认角色
    isSystemDefaultRole(item) {
        // 根据实际业务逻辑判断，可能通过labels、annotations或名称前缀等
        const systemRoles = ['system:', 'cluster-admin', 'admin', 'edit', 'view'];
        return systemRoles.some(prefix => item.metadata.name.startsWith(prefix));
    }
}
```

## 6. Role 列表设计

### 6.1 列表 Schema (pages/rbac-role/role/list/schema.js)

与 ClusterRole 类似，但增加命名空间列和筛选：

```javascript
export default {
    $title: 'Role',
    $withSearchbox: true,
    $withPager: true,
    $withRefresh: true,
    $yamlCreate: 'YAML创建',
    rightToolbarTpl: html`<s-select
        value="{=namespace=}"
        width="200"
        searchable
        datasource="{{namespaceDatasource}}"
        on-change="onNamespaceChange"
    >
    </s-select>`,
    body: {
        api: 'listRequester',
        filter: {
            placeholder: '请输入Role名称进行搜索',
            width: 200,
            $searchbox: {
                keywordType: 'name',
            },
            keywordTypes: [{text: 'Role名称', value: 'name'}],
        },
        $extraPayload: {
            manner: 'page',
        },
        columns: [
            {
                name: 'name',
                label: '名称',
                width: 200,
                render: item => {
                    const {clusterUuid} = this.data.get();
                    return (
                        '<a class="link-item" href="#/cce/cluster/rbac-role/role/detail?' +
                        `clusterUuid=${clusterUuid}&name=${item.name}&namespace=${
                            item.namespace
                        }">${_.escape(item.name)}</a>`
                    );
                },
                fixed: 'left',
            },
            {
                name: 'namespace',
                label: '命名空间',
                width: 150,
            },
            {
                name: 'creationTimestamp',
                label: '创建时间',
                width: 180,
                render: item => utcToTime(item.creationTimestamp),
            },
            {
                name: 'roleType',
                label: '角色类型',
                width: 120,
                render: item => {
                    if (item.isSystemDefault) {
                        return '<span class="system-default-tag">系统默认角色</span>';
                    }
                    return '普通角色';
                },
            },
            {
                name: 'operation',
                label: '操作',
                width: 200,
                fixed: 'right',
            },
        ],
    },
};
```

### 6.2 Role 列表组件实现

与 ClusterRole 类似，但需要处理命名空间相关逻辑：

```javascript
export default class RoleList extends ListPage {
    // 获取默认Role YAML模板
    getDefaultRoleYaml() {
        const namespace = this.data.get('namespace') || 'default';
        return `apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: role-example
  namespace: ${namespace}
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]`;
    }

    // 命名空间变化处理
    onNamespaceChange(namespace) {
        this.data.set('namespace', namespace);
        this.refreshTable();
    }

    // 数据请求
    doRequest(payload) {
        const {namespace, clusterUuid} = this.data.get();
        if (payload.keyword) {
            payload.keyword = payload.keyword.trim();
            payload.pageNo = 1;
        }

        return this.$http.getRoleList({...payload, clusterUuid, namespace}).then(({result}) => {
            const list = this.getData(result) || {};
            return {
                page: {
                    pageNo: result.pageNo || 1,
                    result: list.result,
                    totalCount: result?.totalCount,
                },
            };
        });
    }
}
```

## 7. 详情页设计

### 7.1 ClusterRole 详情页 (pages/rbac-role/cluster-role/detail/index.js)

参考 `pages/workload/deployment/detail/index.js`：

```javascript
@asPage('/cce/cluster/rbac-role/clusterrole/detail')
@invokeAppComp
@invokeBceSanUI
@invokeComp('@cce-page-title', '@cluster-role-detail')
class ClusterRoleDetail extends DetailPage {
    static pageName = 'cce-cluster-role-detail';
    REGION_CHANGE_LOCATION = '#/cce/cluster/list';
    static template = html`
        <app-detail-page>
            <div slot="pageTitle" class="app-page-title">
                <cce-page-title title-data="{=titleData=}" on-fresh="onFresh" />
            </div>
            <div class="cluster-role-detail-content">
                <cluster-role-detail
                    detail="{{detail}}"
                    cluster-uuid="{{clusterUuid}}"
                    cluster-name="{{clusterName}}"
                    on-refresh="getDetail"
                />
            </div>
        </app-detail-page>
    `;

    initData() {
        return {
            titleData: {
                backto: '',
                title: '',
                fresh: true,
            },
            detail: {},
        };
    }

    attached() {
        this.setTitleData();
        this.getDetail();
    }

    setTitleData() {
        const {clusterUuid, name} = this.data.get();
        this.data.set(
            'titleData.backto',
            `#/cce/cluster/rbac-role?clusterUuid=${clusterUuid}&active=clusterrole`,
        );
        this.data.set('titleData.title', name);
    }

    async getDetail() {
        const {clusterUuid, name} = this.data.get();
        this.data.set('loading', true);
        try {
            const result = await this.$http.getClusterRoleDetail({
                clusterUuid,
                name,
            });
            const detail = getData(result);
            this.data.set('detail', detail);
            this.data.set('loading', false);
        } catch (e) {
            this.data.set('loading', false);
        }
    }

    onFresh() {
        this.getDetail();
    }
}
```

### 7.2 详情组件 (pages/rbac-role/cluster-role/detail/detail.js)

参考 `pages/workload/deployment/detail/detail.js`：

```javascript
@asComponent('@cluster-role-detail')
@invokeComp('@app-legend', '@app-detail-cell')
@invokeBceSanUI
class ClusterRoleDetail extends Component {
    static template = html`
        <template>
            <div class="cluster-role-detail-actions">
                <s-button on-click="editYaml" s-if="!detail.isSystemDefault"> 编辑YAML </s-button>
                <s-button on-click="viewYaml" s-if="detail.isSystemDefault"> 查看YAML </s-button>
                <s-button class="button-refresh" on-click="refresh">
                    <s-icon-refresh is-button="{{false}}" />
                </s-button>
            </div>

            <app-legend label="基本信息">
                <app-detail-cell datasource="{{baseInfo}}" divide="{{2}}">
                    <template slot="c-labels">
                        <span s-if="!item.value || item.value.length === 0">-</span>
                        <div s-else class="labels-container">
                            <span s-for="label in item.value" class="label-item">
                                {{label.name}}={{label.value}}
                            </span>
                        </div>
                    </template>
                    <template slot="c-annotations">
                        <span s-if="!item.value || item.value.length === 0">-</span>
                        <div s-else class="annotations-container">
                            <span s-for="annotation in item.value" class="annotation-item">
                                {{annotation.name}}={{annotation.value}}
                            </span>
                        </div>
                    </template>
                    <template slot="c-createAt">{{item.value | getTime}}</template>
                </app-detail-cell>
            </app-legend>

            <app-legend label="规则">
                <s-table
                    columns="{{rulesTable.columns}}"
                    datasource="{{rulesTable.datasource}}"
                    loading="{{loading}}"
                >
                    <template slot="c-resources">
                        <span s-if="!row.resources || row.resources.length === 0">-</span>
                        <span s-else>{{row.resources.join(', ')}}</span>
                    </template>
                    <template slot="c-verbs">
                        <span s-if="!row.verbs || row.verbs.length === 0">-</span>
                        <span s-else>{{row.verbs.join(', ')}}</span>
                    </template>
                    <template slot="c-apiGroups">
                        <span s-if="!row.apiGroups || row.apiGroups.length === 0">-</span>
                        <span s-else>{{row.apiGroups.join(', ')}}</span>
                    </template>
                    <template slot="c-resourceNames">
                        <span s-if="!row.resourceNames || row.resourceNames.length === 0">-</span>
                        <span s-else>{{row.resourceNames.join(', ')}}</span>
                    </template>
                    <template slot="c-nonResourceURLs">
                        <span s-if="!row.nonResourceURLs || row.nonResourceURLs.length === 0"
                            >-</span
                        >
                        <span s-else>{{row.nonResourceURLs.join(', ')}}</span>
                    </template>
                </s-table>
            </app-legend>
        </template>
    `;

    static computed = {
        baseInfo() {
            const detail = this.data.get('detail');
            if (!detail) return [];

            return [
                {
                    name: 'name',
                    label: '名称',
                    value: detail.name,
                },
                {
                    name: 'createAt',
                    label: '创建时间',
                    value: detail.creationTimestamp,
                },
                {
                    name: 'uuid',
                    label: 'UUID',
                    value: detail.uuid,
                },
                {
                    name: 'labels',
                    label: '标签',
                    value: detail.labels,
                },
                {
                    name: 'annotations',
                    label: '注解',
                    value: detail.annotations,
                },
            ];
        },
        rulesTable() {
            const detail = this.data.get('detail');
            return {
                columns: [
                    {
                        name: 'resources',
                        label: '资源',
                        width: 200,
                    },
                    {
                        name: 'verbs',
                        label: '动作',
                        width: 200,
                    },
                    {
                        name: 'nonResourceURLs',
                        label: '非资源URL',
                        width: 200,
                    },
                    {
                        name: 'resourceNames',
                        label: '资源名',
                        width: 200,
                    },
                    {
                        name: 'apiGroups',
                        label: 'API组',
                        width: 200,
                    },
                ],
                datasource: detail?.rules || [],
            };
        },
    };

    editYaml() {
        const {detail, clusterUuid} = this.data.get();
        const dialog = new YamlEditDialog({
            data: {
                title: `编辑ClusterRole - ${detail.name}`,
                clusterUuid,
                name: detail.name,
                resourceType: 'ClusterRole',
            },
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.fire('refresh');
        });
    }

    viewYaml() {
        const {detail, clusterUuid} = this.data.get();
        const dialog = new YamlViewDialog({
            data: {
                title: `查看ClusterRole - ${detail.name}`,
                clusterUuid,
                name: detail.name,
                resourceType: 'ClusterRole',
            },
        });
        dialog.attach(document.body);
    }

    refresh() {
        this.fire('refresh');
    }
}
```

### 7.3 Role 详情页设计

Role 详情页与 ClusterRole 类似，但需要增加命名空间信息：

```javascript
// 基本信息增加命名空间字段
{
    name: 'namespace',
    label: '命名空间',
    value: detail.namespace,
},
```

## 8. YAML 弹框组件设计

### 8.1 YAML 创建弹框 (pages/rbac-role/components/yaml-create-dialog.js)

参考 `components/cluster/workload/yaml-common-create.js`：

```javascript
export default class YamlCreateDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                s-ref="dialog"
                open="{=open=}"
                title="{{title}}"
                class="rbac-role-create-yaml"
                width="1000"
                height="600"
            >
                <div class="create-yaml-tip">
                    通过YAML方式创建{{resourceType}}资源，请编辑下方配置并提交创建。
                </div>
                <div class="yaml-content">
                    <yaml-editor
                        enhanced
                        value="{=yamlContent=}"
                        width="{{950}}"
                        height="{{400}}"
                    />
                </div>

                <div slot="footer">
                    <s-button width="46" on-click="onClose">取消</s-button>
                    <s-button
                        width="46"
                        skin="primary"
                        on-click="onConfirm"
                        loading="{{confirming}}"
                    >
                        创建
                    </s-button>
                </div>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        'yaml-editor': YamlEditor,
    };

    initData() {
        return {
            open: false,
            title: '',
            resourceType: '',
            yamlContent: '',
            confirming: false,
        };
    }

    attached() {
        const {defaultYaml} = this.data.get();
        this.data.set('yamlContent', defaultYaml);
        this.data.set('open', true);
    }

    onClose() {
        this.data.set('open', false);
        this.dispose();
    }

    async onConfirm() {
        const {yamlContent, clusterUuid, resourceType} = this.data.get();

        try {
            this.data.set('confirming', true);

            // 验证YAML格式
            const yamlObj = jsyaml.load(yamlContent);

            // 调用API创建资源
            if (resourceType === 'ClusterRole') {
                await this.$http.createClusterRole({
                    clusterUuid,
                    yaml: yamlContent,
                });
            } else if (resourceType === 'Role') {
                await this.$http.createRole({
                    clusterUuid,
                    yaml: yamlContent,
                });
            }

            Notification.success('创建成功');
            this.fire('confirm');
            this.onClose();
        } catch (e) {
            Notification.error(e.message || '创建失败');
        } finally {
            this.data.set('confirming', false);
        }
    }
}
```

### 8.2 YAML 编辑弹框 (pages/rbac-role/components/yaml-edit-dialog.js)

参考 `components/cluster/workload/edit-yaml.js`：

```javascript
export default class YamlEditDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                s-ref="dialog"
                open="{=open=}"
                title="{{title}}"
                class="rbac-role-edit-yaml"
                width="1000"
                height="600"
            >
                <div class="yaml-content">
                    <yaml-editor
                        enhanced
                        value="{=yamlContent=}"
                        width="{{950}}"
                        height="{{450}}"
                    />
                </div>

                <div slot="footer">
                    <s-button width="46" on-click="onClose">取消</s-button>
                    <s-button
                        width="46"
                        skin="primary"
                        on-click="onConfirm"
                        loading="{{confirming}}"
                    >
                        更新
                    </s-button>
                </div>
            </s-dialog>
        </template>
    `;

    initData() {
        return {
            open: false,
            title: '',
            yamlContent: '',
            confirming: false,
        };
    }

    async attached() {
        const {clusterUuid, name, resourceType, namespace} = this.data.get();

        try {
            // 获取当前资源的YAML
            let result;
            if (resourceType === 'ClusterRole') {
                result = await this.$http.getClusterRoleYaml({clusterUuid, name});
            } else if (resourceType === 'Role') {
                result = await this.$http.getRoleYaml({clusterUuid, name, namespace});
            }

            this.data.set('yamlContent', result.yaml);
            this.data.set('open', true);
        } catch (e) {
            Notification.error('获取YAML失败');
            this.dispose();
        }
    }

    async onConfirm() {
        const {yamlContent, clusterUuid, name, resourceType, namespace} = this.data.get();

        try {
            this.data.set('confirming', true);

            // 验证YAML格式
            const yamlObj = jsyaml.load(yamlContent);

            // 调用API更新资源
            if (resourceType === 'ClusterRole') {
                await this.$http.updateClusterRole({
                    clusterUuid,
                    name,
                    yaml: yamlContent,
                });
            } else if (resourceType === 'Role') {
                await this.$http.updateRole({
                    clusterUuid,
                    name,
                    namespace,
                    yaml: yamlContent,
                });
            }

            Notification.success('更新成功');
            this.fire('confirm');
            this.onClose();
        } catch (e) {
            Notification.error(e.message || '更新失败');
        } finally {
            this.data.set('confirming', false);
        }
    }
}
```

### 8.3 YAML 查看弹框 (pages/rbac-role/components/yaml-view-dialog.js)

```javascript
export default class YamlViewDialog extends Component {
    static template = html`
        <template>
            <s-dialog
                s-ref="dialog"
                open="{=open=}"
                title="{{title}}"
                class="rbac-role-view-yaml"
                width="1000"
                height="600"
                foot="{{false}}"
            >
                <div class="yaml-content">
                    <yaml-editor
                        enhanced
                        value="{{yamlContent}}"
                        width="{{950}}"
                        height="{{500}}"
                        readonly="{{true}}"
                    />
                </div>

                <div class="dialog-actions">
                    <s-button on-click="onClose">关闭</s-button>
                </div>
            </s-dialog>
        </template>
    `;

    async attached() {
        const {clusterUuid, name, resourceType, namespace} = this.data.get();

        try {
            // 获取当前资源的YAML
            let result;
            if (resourceType === 'ClusterRole') {
                result = await this.$http.getClusterRoleYaml({clusterUuid, name});
            } else if (resourceType === 'Role') {
                result = await this.$http.getRoleYaml({clusterUuid, name, namespace});
            }

            this.data.set('yamlContent', result.yaml);
            this.data.set('open', true);
        } catch (e) {
            Notification.error('获取YAML失败');
            this.dispose();
        }
    }
}
```

## 9. 删除确认弹框设计

### 9.1 ClusterRole 删除确认 (pages/rbac-role/cluster-role/list/delete-cluster-role.js)

参考 `pages/hpa/hpa/list/delete-hpa.js`：

```javascript
export default class DeleteClusterRole extends Component {
    static template = html`
        <template>
            <s-dialog
                open="{=open=}"
                title="删除ClusterRole"
                class="delete-cluster-role-dialog"
                on-confirm="onConfirm"
                on-close="onClose"
                confirming="{{confirming}}"
            >
                <div class="delete-tip">
                    <s-icon-warning class="warning-icon" />
                    <div class="message">
                        <p>删除后无法恢复！</p>
                        <p>请确认删除ClusterRole</p>
                    </div>
                </div>

                <s-table
                    columns="{{table.columns}}"
                    datasource="{{table.datasource}}"
                    class="delete-info-table"
                >
                </s-table>
            </s-dialog>
        </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-table': Table,
        's-icon-warning': OutlinedWarning,
    };

    initData() {
        return {
            open: false,
            confirming: false,
            table: {
                columns: [
                    {name: 'name', label: 'ClusterRole名称'},
                    {name: 'creationTimestamp', label: '创建时间'},
                ],
                datasource: [],
            },
        };
    }

    attached() {
        const {name, creationTimestamp} = this.data.get();
        this.data.set('table.datasource', [
            {
                name,
                creationTimestamp: utcToTime(creationTimestamp),
            },
        ]);
        this.data.set('open', true);
    }

    onClose() {
        this.data.set('open', false);
        this.dispose();
    }

    async onConfirm() {
        const {clusterUuid, name} = this.data.get();

        try {
            this.data.set('confirming', true);

            await this.$http.deleteClusterRole({
                clusterUuid,
                name,
            });

            Notification.success('删除成功');
            this.fire('confirm');
            this.onClose();
        } catch (e) {
            Notification.error(e.message || '删除失败');
        } finally {
            this.data.set('confirming', false);
        }
    }
}
```

### 9.2 Role 删除确认 (pages/rbac-role/role/list/delete-role.js)

与 ClusterRole 类似，但增加命名空间信息：

```javascript
// 表格列配置增加命名空间
columns: [
    {name: 'name', label: 'Role名称'},
    {name: 'namespace', label: '命名空间'},
    {name: 'creationTimestamp', label: '创建时间'},
],
    // 数据源增加命名空间
    {
        name,
        namespace,
        creationTimestamp: utcToTime(creationTimestamp),
    };
```

## 10. API 接口设计

### 10.1 ClusterRole 相关接口

```javascript
// 获取ClusterRole列表
getClusterRoleList(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/clusterroles`,
        method: 'GET',
        params: {
            pageNo: params.pageNo,
            pageSize: params.pageSize,
            keyword: params.keyword,
        },
    });
}

// 获取ClusterRole详情
getClusterRoleDetail(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/clusterroles/${params.name}`,
        method: 'GET',
    });
}

// 获取ClusterRole YAML
getClusterRoleYaml(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/clusterroles/${params.name}/yaml`,
        method: 'GET',
    });
}

// 创建ClusterRole
createClusterRole(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/clusterroles`,
        method: 'POST',
        data: {
            yaml: params.yaml,
        },
    });
}

// 更新ClusterRole
updateClusterRole(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/clusterroles/${params.name}`,
        method: 'PUT',
        data: {
            yaml: params.yaml,
        },
    });
}

// 删除ClusterRole
deleteClusterRole(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/clusterroles/${params.name}`,
        method: 'DELETE',
    });
}
```

### 10.2 Role 相关接口

```javascript
// 获取Role列表
getRoleList(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/namespaces/${params.namespace}/roles`,
        method: 'GET',
        params: {
            pageNo: params.pageNo,
            pageSize: params.pageSize,
            keyword: params.keyword,
        },
    });
}

// 获取Role详情
getRoleDetail(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/namespaces/${params.namespace}/roles/${params.name}`,
        method: 'GET',
    });
}

// 获取Role YAML
getRoleYaml(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/namespaces/${params.namespace}/roles/${params.name}/yaml`,
        method: 'GET',
    });
}

// 创建Role
createRole(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/roles`,
        method: 'POST',
        data: {
            yaml: params.yaml,
        },
    });
}

// 更新Role
updateRole(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/namespaces/${params.namespace}/roles/${params.name}`,
        method: 'PUT',
        data: {
            yaml: params.yaml,
        },
    });
}

// 删除Role
deleteRole(params) {
    return this.request({
        url: `/api/v1/clusters/${params.clusterUuid}/namespaces/${params.namespace}/roles/${params.name}`,
        method: 'DELETE',
    });
}
```

## 11. 样式设计

### 11.1 主要样式文件 (pages/rbac-role/style.less)

```less
.cce-rbac-role-wrap {
    .s-tabs {
        .s-tabpane {
            padding: 0;
        }
    }
}

// 系统默认角色标识
.system-default-tag {
    display: inline-block;
    padding: 2px 8px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.2;
}

// 详情页操作按钮
.cluster-role-detail-actions,
.role-detail-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;

    .button-refresh {
        border: none;
        background: none;
        color: #666;

        &:hover {
            color: #144bcc;
        }
    }
}

// 标签和注解容器
.labels-container,
.annotations-container {
    .label-item,
    .annotation-item {
        display: inline-block;
        margin: 2px 4px 2px 0;
        padding: 2px 6px;
        background-color: #f5f5f5;
        border-radius: 3px;
        font-size: 12px;
        color: #333;
    }
}

// 删除确认弹框
.delete-cluster-role-dialog,
.delete-role-dialog {
    .delete-tip {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;

        .warning-icon {
            color: #ff9500;
            margin-right: 8px;
            margin-top: 2px;
        }

        .message {
            p {
                margin: 0 0 4px 0;

                &:first-child {
                    font-weight: 500;
                    color: #ff9500;
                }
            }
        }
    }

    .delete-info-table {
        margin-top: 16px;
    }
}

// YAML弹框样式
.rbac-role-create-yaml,
.rbac-role-edit-yaml,
.rbac-role-view-yaml {
    .create-yaml-tip {
        margin-bottom: 16px;
        padding: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #666;
        font-size: 14px;
    }

    .yaml-content {
        position: relative;
    }

    .dialog-actions {
        display: flex;
        justify-content: flex-end;
        padding: 16px 0 0 0;
        border-top: 1px solid #e8e8e8;
        margin-top: 16px;
    }
}
```

## 12. 数据处理设计

### 12.1 ClusterRole 数据处理 (pages/rbac-role/cluster-role/list/field.js)

```javascript
export function getData(result) {
    const resultList = [];
    if (result?.items) {
        for (const item of result.items) {
            resultList.push({
                name: item.metadata.name,
                creationTimestamp: item.metadata.creationTimestamp,
                isSystemDefault: isSystemDefaultRole(item),
                rules: item.rules || [],
                labels: formatLabels(item.metadata.labels),
                annotations: formatAnnotations(item.metadata.annotations),
                uuid: item.metadata.uid,
                item,
            });
        }
    }
    return {
        totalCount: result.totalCount,
        pageNo: result.pageNo,
        pageSize: result.pageSize,
        result: resultList,
    };
}

// 判断是否为系统默认角色
export function isSystemDefaultRole(item) {
    const systemRoles = ['system:', 'cluster-admin', 'admin', 'edit', 'view'];
    return systemRoles.some(prefix => item.metadata.name.startsWith(prefix));
}

// 格式化标签
export function formatLabels(labels) {
    if (!labels) return [];
    return Object.entries(labels).map(([name, value]) => ({
        name,
        value,
    }));
}

// 格式化注解
export function formatAnnotations(annotations) {
    if (!annotations) return [];
    return Object.entries(annotations).map(([name, value]) => ({
        name,
        value,
    }));
}
```

### 12.2 详情页数据处理 (pages/rbac-role/cluster-role/detail/field.js)

```javascript
export function getData(result) {
    const item = result.item || result;

    return {
        name: item.metadata.name,
        namespace: item.metadata.namespace,
        creationTimestamp: item.metadata.creationTimestamp,
        uuid: item.metadata.uid,
        isSystemDefault: isSystemDefaultRole(item),
        labels: formatLabels(item.metadata.labels),
        annotations: formatAnnotations(item.metadata.annotations),
        rules: item.rules || [],
    };
}
```

## 13. 实施计划

### 13.1 开发阶段划分

按照需求文档要求，分三个阶段依次完成：

#### 阶段一：角色模块及 Tab 页

**预计工期：2 天**

1. 创建基础目录结构
2. 实现主页面 `pages/rbac-role/index.js`
3. 配置路由和导航栏
4. 实现 Tab 页切换功能
5. 添加基础样式

**交付物：**

-   角色管理主页面
-   Tab 页切换功能
-   导航栏集成

#### 阶段二：ClusterRole 相关功能

**预计工期：3 天**

1. 实现 ClusterRole 列表页面
    - 列表组件和 Schema 配置
    - 搜索和分页功能
    - 系统默认角色标识
2. 实现 YAML 操作功能
    - YAML 创建弹框
    - YAML 编辑弹框
    - YAML 查看弹框
3. 实现删除功能
    - 删除确认弹框
    - 系统默认角色删除限制
4. 实现 ClusterRole 详情页
    - 基本信息展示
    - 规则信息展示
    - 操作按钮

**交付物：**

-   ClusterRole 完整功能模块
-   所有 YAML 操作弹框
-   ClusterRole 详情页

#### 阶段三：Role 相关功能

**预计工期：3 天**

1. 实现 Role 列表页面
    - 列表组件和 Schema 配置
    - 命名空间筛选功能
    - 搜索和分页功能
2. 实现 YAML 操作功能
    - 复用 ClusterRole 的 YAML 弹框组件
    - 适配命名空间相关逻辑
3. 实现删除功能
    - 删除确认弹框（包含命名空间信息）
4. 实现 Role 详情页
    - 基本信息展示（包含命名空间）
    - 规则信息展示
    - 操作按钮

**交付物：**

-   Role 完整功能模块
-   Role 详情页
-   整体功能联调

### 13.2 关键里程碑

| 里程碑               | 时间节点 | 验收标准                                |
| -------------------- | -------- | --------------------------------------- |
| 基础框架完成         | 第 2 天  | Tab 页切换正常，导航栏集成完成          |
| ClusterRole 功能完成 | 第 5 天  | 列表、详情、YAML 操作、删除功能全部正常 |
| Role 功能完成        | 第 8 天  | 所有功能完成，整体联调通过              |
| 测试完成             | 第 10 天 | 所有测试用例通过，代码 review 完成      |

## 14. 测试策略

### 14.1 单元测试

**测试覆盖范围：**

-   数据处理函数（field.js 中的函数）
-   组件状态管理
-   API 调用逻辑

**测试用例示例：**

```javascript
// 测试系统默认角色判断
describe('isSystemDefaultRole', () => {
    it('should return true for system roles', () => {
        const item = {
            metadata: {
                name: 'system:admin',
            },
        };
        expect(isSystemDefaultRole(item)).toBe(true);
    });

    it('should return false for custom roles', () => {
        const item = {
            metadata: {
                name: 'custom-role',
            },
        };
        expect(isSystemDefaultRole(item)).toBe(false);
    });
});
```

### 14.2 集成测试

**测试场景：**

1. 页面路由跳转
2. Tab 页切换
3. 列表数据加载和分页
4. YAML 弹框操作流程
5. 删除确认流程
6. 详情页数据展示

### 14.3 端到端测试

**测试流程：**

1. 进入角色管理页面
2. 切换 ClusterRole 和 Role Tab 页
3. 执行创建、编辑、查看、删除操作
4. 验证详情页功能
5. 验证权限控制（系统默认角色限制）

### 14.4 兼容性测试

**测试范围：**

-   不同浏览器兼容性
-   不同屏幕分辨率适配
-   移动端响应式布局

## 15. 注意事项和风险点

### 15.1 技术风险

1. **YAML 格式验证**

    - 风险：用户输入无效 YAML 导致创建/更新失败
    - 解决方案：使用 js-yaml 库进行客户端验证，提供友好错误提示

2. **权限控制**

    - 风险：系统默认角色被误删除或编辑
    - 解决方案：前端和后端双重校验，禁用相关操作按钮

3. **命名空间数据同步**
    - 风险：Role 列表中命名空间筛选数据不一致
    - 解决方案：统一命名空间数据源，实时更新

### 15.2 用户体验风险

1. **操作反馈**

    - 风险：YAML 操作耗时较长，用户体验差
    - 解决方案：添加 loading 状态，提供操作进度反馈

2. **数据展示**
    - 风险：规则信息过多导致页面展示混乱
    - 解决方案：合理设计表格列宽，支持内容折叠展开

### 15.3 性能风险

1. **列表数据量**

    - 风险：集群中角色数量过多影响页面性能
    - 解决方案：实现分页加载，优化数据渲染

2. **YAML 编辑器性能**
    - 风险：大型 YAML 文件编辑卡顿
    - 解决方案：使用高性能的 ACE 编辑器，限制文件大小

### 15.4 安全风险

1. **YAML 注入**

    - 风险：恶意 YAML 内容导致安全问题
    - 解决方案：后端严格校验 YAML 内容，过滤危险字段

2. **权限越权**
    - 风险：用户创建超出权限范围的角色
    - 解决方案：后端权限校验，前端辅助提示

## 16. 后续优化建议

### 16.1 功能增强

1. **批量操作**

    - 支持批量删除角色
    - 支持批量导出 YAML

2. **模板功能**

    - 提供常用角色模板
    - 支持自定义模板保存

3. **可视化编辑**
    - 提供图形化规则编辑器
    - 支持拖拽式权限配置

### 16.2 用户体验优化

1. **智能提示**

    - YAML 编辑时提供语法提示
    - 权限配置时提供资源建议

2. **操作历史**

    - 记录角色变更历史
    - 支持版本回滚

3. **搜索增强**
    - 支持高级搜索条件
    - 支持标签和注解搜索

### 16.3 监控和告警

1. **操作审计**

    - 记录所有角色操作日志
    - 集成到集群审计系统

2. **异常监控**
    - 监控角色创建失败率
    - 监控权限异常使用

## 17. 总结

本设计文档详细描述了 CCE 控制台角色管理功能的技术实现方案，包括：

1. **完整的功能架构**：涵盖 ClusterRole 和 Role 的全生命周期管理
2. **详细的技术设计**：从页面结构到组件实现的完整方案
3. **规范的代码结构**：参考现有代码风格，保持一致性
4. **完善的测试策略**：确保功能质量和稳定性
5. **明确的实施计划**：分阶段开发，降低风险

通过按照本设计文档实施，可以高质量地完成角色管理功能的开发，为用户提供便捷、安全的 Kubernetes RBAC 资源管理体验。

### 17.1 核心特性

-   **双资源支持**：同时支持 ClusterRole 和 Role 两种 RBAC 资源
-   **完整生命周期**：创建、查看、编辑、删除的完整操作流程
-   **权限控制**：系统默认角色的保护机制
-   **用户友好**：直观的 Tab 页设计和 YAML 编辑体验
-   **高度复用**：组件设计支持代码复用，降低维护成本

### 17.2 技术亮点

-   **参考现有架构**：充分复用现有组件和设计模式
-   **模块化设计**：清晰的目录结构和组件划分
-   **响应式布局**：适配不同屏幕尺寸
-   **性能优化**：分页加载和懒加载机制
-   **错误处理**：完善的异常处理和用户提示

### 17.3 开发建议

1. **严格按阶段开发**：确保每个阶段的质量再进入下一阶段
2. **重视代码复用**：ClusterRole 和 Role 功能有很多相似之处，要充分复用代码
3. **注重用户体验**：特别是 YAML 编辑和权限提示的用户体验
4. **完善测试覆盖**：确保所有功能路径都有测试覆盖
5. **及时沟通反馈**：开发过程中遇到问题及时沟通确认
