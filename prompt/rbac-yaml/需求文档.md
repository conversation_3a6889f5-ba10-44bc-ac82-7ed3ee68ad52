prd

# 需求背景

新增角色功能模块，用于控制台进行集群资源 ClusterRole、Role 的全生命周期管理，包含创建、查看、编辑、删除

# 产品设计

| 功能/页面                                     | 文字交互说明                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | 原型图                                                                                                                                                                                                                                                                                                       |
| --------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 角色-新增角色功能模块-新增 ClusterRole Tab 页 | 在安全管理导航栏位置下新增角色模块，单击进入角色管理页面，支持用户管理 K8s 资源 ClusterRole 和 Role 资源，默认为 ClusterRole Tab 页列表属性：名称：ClusterRole 资源的名称单击名称进入目标 ClusterRole 资源详情页面创建时间角色类型：系统默认角色：列表上新增“系统默认角色”标识，不支持编辑、删除（仅角色管理控制台）定义：由 Kubernetes 自动生成的角色（待定）普通角色：无相关标识，支持编辑、删除操作：创建：支持通过 YAML 方式创建 ClusterRole 资源，单击系统弹出创建 YAML 弹框搜索：支持按照资源名称模糊搜索查看 YAML：针对系统默认角色支持查看 YAML 配置编辑 YAML：针对普通角色支持编辑 YAML 配置删除：系统默认角色不支持删除，禁止单击，鼠标悬停显示提示文案：系统默认角色不支持删除分页：支持分页展示和页面跳转 | ![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=929e463835184f9099dbabed38dd7d75&docGuid=efp8wrNF05gkXV)                                                                                                                                                                |
|                                               | 单击创建操作系统弹出创建 YAML 配置框，支持通过 YAML 方式创建资源，默认 YAML 配置见下置                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=d1b66a84faec4906b6f9820be81cb51a&docGuid=efp8wrNF05gkXV)                                                                                                                                                                |
|                                               | 单击编辑 YAML 操作系统弹出编辑 YAML 配置框，支持编辑 YAML 配置确定：单击提交最新 YAML 配置取消：单击直接关闭弹框单击查看 YAML 操作系统弹出查看 YAML 配置框，不支持编辑 YAML 配置关闭：单击直接关闭弹框取消：单击直接关闭弹框                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | ![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=ace76b5db66245a1ae6a1d82442bc4bc&docGuid=efp8wrNF05gkXV)编辑 YAML![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=ab2c6edde1f44efc9d4600797de63ee8&docGuid=efp8wrNF05gkXV)查看 YAML |
|                                               | 单击删除操作系统弹出删除资源确认框，弹出确认框显示删除资源的名称和创建时间                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | ![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=42418d7bd26b4c15b41fad5f01016a0e&docGuid=efp8wrNF05gkXV)                                                                                                                                                                |
|                                               | 单击目标资源名称进入详情页面，详情页面分为基本信息和规则两个部分：基本信息：名称创建时间 UUID 标签：无数据时展示“-”注解：无数据时展示“-”规则：不进行分页，列表及属性信息全部平铺展示资源动作非资源 URL 资源名 API 组操作：编辑 YAML：根据角色类型展示，与列表操作保持一致查看 YAML ：根据角色类型展示，与列表操作保持一致刷新：单击刷新详情页面                                                                                                                                                                                                                                                                                                                                                                       | ![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=42af8addbeee406ebd4aec5fd0148d45&docGuid=efp8wrNF05gkXV)                                                                                                                                                                |
| 角色-新增角色功能模块-新增 Role Tab 页        | 在安全管理导航栏位置下新增角色模块，单击进入角色管理页面，单击 Role 进入管理 Role 资源 Tab 页列表属性：名称：Role 资源的名称单击名称进入目标 Role 资源详情页面命名空间：展示资源所属命名空间创建时间角色类型：系统默认角色：列表上新增“系统默认角色”标识，不支持编辑、删除普通角色：无相关标识，支持编辑、删除操作：创建：支持通过 YAML 方式创建 Role 资源，单击系统弹出创建 YAML 弹框命名空间筛选框：支持按照命名空间进行筛选，支持模糊搜索命名空间搜索：支持按照资源名称模糊搜索查看 YAML：针对系统默认角色支持查看 YAML 配置编辑 YAML：针对普通角色支持编辑 YAML 配置删除：系统默认角色不支持删除，禁止单击，鼠标悬停显示提示文案：系统默认角色不支持删除分页：支持分页展示和页面跳转                              | ![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=c260a0189d484dd2a33513bbd22a02db&docGuid=efp8wrNF05gkXV)                                                                                                                                                                |
|                                               | 单击创建操作系统弹出创建 YAML 配置框，支持通过 YAML 方式创建资源，默认 YAML 配置见下置                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=f2d547b4a51040b29e281f55e5e3c5e5&docGuid=efp8wrNF05gkXV)                                                                                                                                                                |
|                                               | 单击编辑 YAML 操作系统弹出编辑 YAML 配置框，支持编辑 YAML 配置确定：单击提交最新 YAML 配置取消：单击直接关闭弹框单击查看 YAML 操作系统弹出查看 YAML 配置框，不支持编辑 YAML 配置关闭：单击直接关闭弹框取消：单击直接关闭弹框                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | 略                                                                                                                                                                                                                                                                                                           |
|                                               | 单击删除操作系统弹出删除资源确认框，弹出确认框显示删除资源的名称、命名空间和创建时间                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | ![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=8e9f29fbe13b432791e52b8e6b9f5874&docGuid=efp8wrNF05gkXV)                                                                                                                                                                |
|                                               | 单击目标资源名称进入详情页面，详情页面分为基本信息和规则两个部分：基本信息：名称命名空间创建时间 UUID 标签：无数据时展示“-”注解：无数据时展示“-”规则：不进行分页，列表及属性信息全部平铺展示资源动作非资源 URL 资源名 API 组操作：编辑 YAML：根据角色类型展示，与列表操作保持一致查看 YAML ：根据角色类型展示，与列表操作保持一致刷新：单击刷新详情页面                                                                                                                                                                                                                                                                                                                                                               | ![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=d0c46c2982cf47d5bd7a667584f226a3&docGuid=efp8wrNF05gkXV)                                                                                                                                                                |

# 代码要求

请尽可能参考已有的样式和交互代码，请先在代码库搜索对应相关代码，再进行参考，例如：

-   角色界面 Tab 页上方 "角色" 标题样式和其他界面标题样式一致
-   角色界面 Tab 页样式和 "自动伸缩" 页面中 "水平伸缩（HPA）" 和 "定时水平伸缩（CronHPA）" Tab 页样式相同
-   ClusterRole 列表和"自定义资源"中选择 rbac.authorization.k8s.io/v1/ClusterRole 资源列表的内容和操作类似，区分在于没有 "命名空间" 列
-   Role 列表和"自定义资源"中选择 rbac.authorization.k8s.io/v1/Role 资源列表的内容和操作一致
-   ClusterRole 和 Role 的详情页和工作负载详情页类似，但内容要按照产品设计展示

# 其他说明

-   创建 ClusterRole 的默认配置模版为:

```
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: clusterRole-example
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
```

-   创建 Role 的默认配置模版为:

```
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: role-example
  namespace: default
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
```

-   请按步骤依次完成功能：

    -   完成 "角色" 模块及 tab 页
    -   完成 ClusterRole 相关功能
    -   完成 Role 相关功能

-   若有疑惑问题请向我确认后再输出结果
