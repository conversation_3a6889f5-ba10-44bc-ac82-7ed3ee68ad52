import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Tooltip, Notification} from '@baidu/sui';
import {ACEEditor} from '@baiducloud/bce-ui/san';
import {ClipBoard} from '@baidu/sui-biz';
import {OutlinedCopy} from '@baidu/sui-icon';
import FullScreen from '../full-screen';

import './index.less';

const template = html`<template>
    <div class="yaml-editor" style="{{style}}">
        <template s-if="enhanced">
            <full-screen isFullScreen="{=isFullScreen=}">
                <template>
                    <ui-aceeditor
                        s-ref="aceeditor"
                        value="{=value=}"
                        class="aceeditor"
                        height="{{_height}}"
                        mode="ace/mode/yaml"
                        theme="ace/theme/chrome"
                        wrap="free"
                        showGutter="{{true}}"
                        showPrintMargin="{{false}}"
                    />
                </template>
                <template slot="action">
                    <div class="action-item" on-click="onCopy">
                        <s-icon-copy />
                    </div>
                </template>
            </full-screen>
        </template>
        <template s-else>
            <s-tooltip class="copy" content="复制" s-if="showCopyBtn">
                <s-icon-copy on-click="onCopy" />
            </s-tooltip>
            <ui-aceeditor
                s-ref="aceeditor"
                value="{=value=}"
                class="aceeditor"
                height="{{_height}}"
                mode="ace/mode/yaml"
                theme="ace/theme/chrome"
                wrap="free"
                showGutter="{{true}}"
                showPrintMargin="{{false}}"
            />
        </template>
    </div>
</template>`;

export default class YamlEditor extends Component {
    static template = template;

    static components = {
        'ui-aceeditor': ACEEditor,
        's-clip-board': ClipBoard,
        's-icon-copy': OutlinedCopy,
        'full-screen': FullScreen,
        's-tooltip': Tooltip,
    };

    static computed = {
        _height() {
            const isFullScreen = this.data.get('isFullScreen');
            if (isFullScreen) {
                return window.screen.height - 30;
            }
            return this.data.get('height') || 400;
        },
        style() {
            const width = this.data.get('width');
            const isFullScreen = this.data.get('isFullScreen');
            if (isFullScreen) {
                return;
            }
            if (width) {
                return `width:${width}px;`;
            } else {
                return 'min-width:800px;';
            }
        },
    };

    initData() {
        return {
            showCopyBtn: true,
            isFullScreen: false,
            value: '',
        };
    }

    inited() {}

    copyText(text) {
        window.navigator?.clipboard?.writeText(text).then(() => {
            Notification.success('复制成功');
        });
    }

    onCopy() {
        this.copyText(this.data.get('value'));
    }
}
