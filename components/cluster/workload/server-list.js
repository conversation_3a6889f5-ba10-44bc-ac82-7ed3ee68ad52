/**
 * @file server-list.js
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';

import {utcToTime} from '../../../utils/util';
import {getTemplate, FrontPage} from './front-page';

/* eslint-disable */
const slot = html`
    <template slot="c-name">
        <a
            href="#/cce/flow/service/detail?clusterUuid={{row.clusterUuid}}&namespaceName={{row.namespaceName}}&serviceName={{row.name}}"
        >
            {{row.name}}
        </a>
    </template>
    <div slot="c-internalEndpoint">
        <p class="text-overflow-ellipsis" s-for="r in row.internalEndpoint">{{r}}</p>
    </div>
    <div slot="c-externalEndpoint">
        <p s-for="r in row.externalEndpoint">
            <a target="_blank" href="http://{{r}}">{{r}}</a>
        </p>
    </div>
    <template slot="c-createAt">
        <div>{{row.createAt | toTime}}</div>
    </template>
`;
const template = html`
    <template>
        <s-legend label="关联服务">${getTemplate(slot)}</s-legend>
    </template>
`;
/* eslint-enable */
// 采取前端分页，是因为后端拿到的数据是全量的，后端分页的话改动比较大，而且性能也没有多大优势
export default class extends FrontPage {
    static template = template;

    static components = _.extend(super.components, {
        's-legend': AppLegend
    });

    initData() {
        const data = super.initData();
        return _.extend({}, data, {
            table: {
                datasource: [],
                datasourceClone: [],
                columns: [
                    {
                        name: 'name',
                        label: '服务名称',
                        width: '120'
                    },
                    {
                        name: 'type',
                        label: '类型',
                        width: '100'
                    },
                    {
                        name: 'clusterIP',
                        label: '集群IP',
                        width: '150'
                    },
                    {
                        name: 'internalEndpoint',
                        label: '内部端点',
                        width: '200'
                    },
                    {
                        name: 'externalEndpoint',
                        label: '外部端点',
                        width: '150'
                    },
                    {
                        name: 'createAt',
                        label: '创建时间',
                        width: '150'
                    }
                ]
            }
        });
    }

    static filters = {
        toTime(value) {
            return utcToTime(value);
        }
    };

    attached() {
        super.attached();
        this.loadDetailData();
        this.watch('detail', value => {
            this.loadDetailData(value);
        });
    }

    // 处理详情信息
    loadDetailData(value) {
        const detail = value || this.data.get('detail');
        if (Object.keys(detail).length === 0) {
            return;
        }
        this.data.set('table.datasourceClone', detail.services);
        this.handlePagerChange();
    }
}
