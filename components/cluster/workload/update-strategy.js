/**
 * @file update-strategy.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, Input, Select, InputNumber, Radio} from '@baidu/sui';
import {Tip, AppLegend} from '@baidu/sui-biz';

import {HealthyCheck} from '../../../utils/enums';

import tips from '../../../utils/tips';

/* eslint-disable */
const template = html`
    <template>
        <div class="worklaod-update-strategy">
            <s-legend label="更新策略">
                <s-form
                    class="update-strategy-form"
                    s-ref="updateStrategy"
                    data="{=formData=}"
                    rules="{{rules}}"
                    isRequired="{{false}}"
                    label-align="left"
                >
                    <s-form-item label="更新方式：">
                        <s-radio-group
                            value="{=formData.type=}"
                            radioType="button"
                            datasource="{{rollingUpdateType}}"
                        />

                        <p class="color-gray-light mt10">{{rollingUpdateTip}}</p>
                    </s-form-item>
                    <template s-if="formData.type === 'RollingUpdate'">
                        <s-form-item label="最大不可用Pod数：">
                            <s-input-number min="{{0}}" value="{=formData.maxUnavailable=}" />
                            <s-select value="{=formData.maxUnavailableUnit=}" datasource="{{unitList}}" width="60" />
                            <s-tip
                                class="tip"
                                placement="right"
                                skin="warning"
                                content="${tips.workload.maxUnavailableTip}"
                            />
                        </s-form-item>
                        <s-form-item label="最大超出期望Pod数：">
                            <s-input-number min="{{0}}" value="{=formData.maxSurge=}" />
                            <s-select value="{=formData.maxSurgeUnit=}" datasource="{{unitList}}" width="60" />
                            <s-tip
                                class="tip"
                                placement="right"
                                skin="warning"
                                content="${tips.workload.maxSurgeTip}"
                            />
                        </s-form-item>
                    </template>
                    <s-form-item label="最小准备时间：">
                        <s-input-number min="{{0}}" value="{=formData.minReadySeconds=}" />
                        <span class="unit">秒</span>
                        <s-tip
                            class="tip"
                            placement="right"
                            skin="warning"
                            content="${tips.workload.minReadySecondsTip}"
                        />
                    </s-form-item>
                </s-form>
            </s-legend>
        </div>
    </template>
`;

/* eslint-enable */
export default class extends Component {
    static template = template;

    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-select': Select,
        's-input-number': InputNumber,
        's-tip': Tip,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-legend': AppLegend
    };

    initData() {
        return {
            formData: {
                type: 'RollingUpdate',
                maxUnavailable: 25,
                maxUnavailableUnit: '%',
                maxSurge: 25,
                maxSurgeUnit: '%',
                minReadySeconds: 0
            },
            rules: {},
            rollingUpdateType: [
                {
                    text: '滚动更新',
                    value: 'RollingUpdate',
                    tip: tips.workload.rollingUpdate
                },
                {
                    text: '替换更新',
                    value: 'Recreate',
                    tip: tips.workload.recreate
                }
            ],
            unitList: [
                {
                    text: '%',
                    value: '%'
                },
                {
                    text: '个',
                    value: ''
                }
            ]
        };
    }
    static computed = {
        rollingUpdateTip() {
            const rollingUpdateType = this.data.get('rollingUpdateType');
            const type = this.data.get('formData.type');
            let rollingUpdateTip = '';
            _.each(rollingUpdateType, item => {
                if (type === item.value) {
                    rollingUpdateTip = item.tip;
                    return false;
                }
            });
            return rollingUpdateTip;
        }
    };

    async attached() {
        this.watch('formData.typeValue', value => {
            // HTTP检查，健康阀值固定为1
            if (value === HealthyCheck.httpGet) {
                this.data.set('formData.successThreshold', 1);
            }
        });
    }

    // 校验表单
    async validateForm() {
        const updateStrategyRef = this.ref('updateStrategy');
        await updateStrategyRef.validateFields();
    }

    getFormData() {
        const formData = this.data.get('formData');
        const updateStrategy = {
            type: formData.type
        };
        if (formData.type === 'RollingUpdate') {
            const maxUnavailable = formData.maxUnavailableUnit
                ? formData.maxUnavailable + formData.maxUnavailableUnit
                : formData.maxUnavailable;
            const maxSurge = formData.maxUnavailableUnit
                ? formData.maxSurge + formData.maxUnavailableUnit
                : formData.maxSurge;
            _.extend(updateStrategy, {
                rollingUpdate: {
                    maxUnavailable: maxUnavailable,
                    maxSurge: maxSurge
                }
            });
        } else {
            _.extend(updateStrategy, {minReadySeconds: formData.minReadySeconds});
        }
        return updateStrategy;
    }
}
