/**
 * @file update.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Dialog, Notification, Tabs} from '@baidu/sui';
import {AppLegend} from '@baidu/sui-biz';
import {WorkloadType} from '../../../utils/enums';
import ContainerHybridConfig from './container-hybrid-config';
import HistoryVersion from './history-version';

import {addSecret} from './data-flavor';

/* eslint-disable */
const template = html`
    <template>
        <div class="update-container-page">
            <s-tabs
                position="top"
                class="workload-update-tabs"
                s-if="moduleName === '${WorkloadType.Deployment}'"
            >
                <s-tabpane key="container" label="容器配置" lazy>
                    <container-hybrid-config
                        detail="{{detail}}"
                        clusterUuid="{{detail.clusterUuid}}"
                        workloadType="{{moduleName}}"
                        containerSelected="{{containerSelected}}"
                        s-ref="container-hybrid-config"
                    />
                    <div class="button-wrapper">
                        <s-button
                            class="submit-btn"
                            disabled="{{submitDisabled}}"
                            skin="primary"
                            size="large"
                            on-click="onSubmit"
                        >
                            提交
                        </s-button>
                    </div>
                </s-tabpane>
                <s-tabpane key="history" label="历史版本" lazy>
                    <history-version detail="{{detail}}" on-refresh="onRefresh" />
                </s-tabpane>
            </s-tabs>
            <template s-else>
                <container-hybrid-config
                    detail="{{detail}}"
                    clusterUuid="{{detail.clusterUuid}}"
                    workloadType="{{moduleName}}"
                    containerSelected="{{containerSelected}}"
                    s-ref="container-hybrid-config"
                />
                <div class="button-wrapper">
                    <s-button
                        class="submit-btn"
                        disabled="{{submitDisabled}}"
                        skin="primary"
                        size="large"
                        on-click="onSubmit"
                    >
                        提交
                    </s-button>
                </div>
            </template>
        </div>
    </template>
`;
/* eslint-enable */
export default class extends Component {
    static template = template;

    static components = {
        's-legend': AppLegend,
        's-button': Button,
        'container-hybrid-config': ContainerHybridConfig,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        'history-version': HistoryVersion,
    };

    initData() {
        return {
            submitDisabled: false,
        };
    }

    onRefresh() {
        this.fire('refresh');
    }

    getContainerKey(moduleName, key) {
        let prefix = 'spec.template.spec.';
        if (WorkloadType.CronJob === moduleName) {
            prefix = 'spec.jobTemplate.spec.template.spec.';
        }

        return `${prefix}${key}`;
    }

    getHybridKey(moduleName) {
        let prefix = 'spec.template.metadata.annotations';
        if (WorkloadType.CronJob === moduleName) {
            prefix = 'spec.jobTemplate.spec.template.metadata.annotations';
        }

        return `${prefix}`;
    }

    async onSubmit() {
        try {
            this.data.set('submitDisabled', true);
            const containerConfig = this.ref('container-hybrid-config');
            await containerConfig.validateForm();
            const {name, clusterUuid, namespace} = this.data.get('detail');
            const {moduleName} = this.data.get();
            const containerKey = this.getContainerKey(moduleName, 'containers');
            const {yamlContent, certificates} = await containerConfig.getYamlContent();
            if (!yamlContent) {
                return;
            }
            const containers = _.get(yamlContent, containerKey, []);
            if (!containers.length) {
                Notification.error('容器组中不能全为初始化容器');
                return;
            }
            await Dialog.warning({
                content: '请确定是否要更新工作负载配置？',
            });
            const secretPayload = {
                clusterUuid,
                namespace,
                certificates,
            };
            await addSecret(secretPayload);

            const payload = {
                moduleName,
                name,
                clusterUuid,
                namespaceName: namespace,
                content: yamlContent,
            };
            await this.$http.editAppYaml(payload);
            Notification.success('修改成功');
            this.fire('refresh');
        } catch (error) {
        } finally {
            this.data.set('submitDisabled', false);
        }
    }
}
