/**
 * 镜像版本选择
 *
 * @file select-version.js
 * <AUTHOR>
 */

import {Base64} from 'js-base64';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Alert, Dialog, Pagination, Radio, Search, Select, Table, Tabs} from '@baidu/sui';
import {SearchBox} from '@baidu/sui-biz';


export default class SelectVersion extends Component {
    static template = html`
        <template>
            <s-dialog
                s-ref="dialog"
                open="{=open=}"
                confirm-button-disable="{{selection.selectedIndex.length === 0}}"
                title="选择版本"
                width="1000"
                on-confirm="onConfirm"
            >
                <div class="cce-workload-selectimage">
                    <div class="toolbar">
                        <s-search
                            value="{=keyword=}"
                            placeholder="请输入镜像版本进行搜索"
                            on-search="onSearch"
                        />
                    </div>

                    <s-table
                        selection="{=selection=}"
                        columns="{{table.columns}}"
                        datasource="{{table.datasource}}}"
                        loading="{{table.loading}}"
                    >
                    </s-table>

                    <div class="pagination">
                        <s-pagination
                            total="{{pager.count}}"
                            layout="pageSize, pager, total, go"
                            page-sizes="{{pager.pageSizes}}"
                            page="{=pager.page=}"
                            page-size="{=pager.size=}"
                            on-pagerChange="onPagerChange"
                            on-pagerSizeChange="onPageSizeChange"
                        >
                        </s-pagination>
                    </div>
                </div>
            </s-dialog>
        </template>
    `;

    static components = {
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-dialog': Dialog,
        's-searchbox': SearchBox,
        's-table': Table,
        's-pagination': Pagination,
        's-alert': Alert,
        's-select': Select,
        's-search': Search,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
    };

    initData() {
        return {
            open: true,
            keyword: '',
            keywordTypes: {
                company: 'tagName',
                userImage: 'name',
                bceImage: 'name',
                aiImage: 'name',
            },
            selection: {
                mode: 'single',
                selectedIndex: [],
            },
            table: {
                columns: [
                    {
                        name: 'tagName',
                        label: '镜像版本',
                        width: '150',
                    },
                    {
                        name: 'digest',
                        label: 'digest',
                        width: '400',
                    },
                ],
                datasource: [],
                loading: false,
            },
            pager: {
                size: 10,
                page: 1,
                count: 1,
                pageSizes: [10, 20, 50, 100],
            },
        };
    }

    static computed = {
        isCCRCompany() {
            return this.data.get('image.imageType') === 'company';
        },
    };

    inited() {
        this.getVersionList();
    }

    onPagerChange(e) {
        const {value} = e;
        this.data.set('pager.page', value.page);
        this.getVersionList(false);
    }

    onPageSizeChange(e) {
        const {value} = e;
        this.data.set('pager.size', value.pageSize);
        this.getVersionList();
    }

    onSearch() {
        this.getVersionList();
    }

    getQuerys() {
        const {image, isCCRCompany, pager, keyword, keywordTypes} = this.data.get();
        const {imageType, instanceId, projectName, projectId, repositoryName} = image;
        const payload = {
            pageNo: pager.page,
            pageSize: pager.size,
        };

        if (keyword) {
            payload.keyword = keyword;
            payload.keywordType = keywordTypes[imageType];

            if (isCCRCompany) {
                payload.tagName = keyword;
            }
        }

        if (isCCRCompany) {
            payload.instanceId = instanceId;
            payload.projectName = projectName;
            payload.repositoryName = Base64.toBase64(repositoryName, true);
        } else {
            payload.projectId = projectId;
            payload.repositoryName = repositoryName;
        }

        return payload;
    }

    getVersionList(resetPageSize = true) {
        this.data.set('table.error', false);
        this.data.set('selection.selectedIndex', []);
        if (resetPageSize) {
            this.data.set('pager.page', 1);
        }

        const isCCRCompany = this.data.get('isCCRCompany');

        this.data.set('table.loading', true);
        this[isCCRCompany ? 'getCCRCompanyRepositoryTags' : 'getCCRRepositoryTags']()
            .then((result) => {
                // 接口返回格式不同
                const datasource = result.items || [];
                this.data.set('table.datasource', datasource);
                this.data.set('pager.count', result.totalCount || result.total || 0);
                this.data.set('table.loading', false);

                if (datasource.length > 0) {
                    this.data.set('selection.selectedIndex', [0]);
                }
            })
            .catch(e => {
                this.data.set('table.error', true);
                this.data.set('table.datasource', []);
                this.data.set('pager.count', 0);
            })
            .finally(() => {
                this.data.set('table.loading', false);
            });
    }

    // 获取企业版镜像版本
    getCCRCompanyRepositoryTags() {
        const {image, pager, keyword} = this.data.get();
        const {instanceId, projectName, repositoryName} = image;
        const payload = {
            pageNo: pager.page,
            pageSize: pager.size,
            instanceId,
            projectName,
            repositoryName: Base64.toBase64(repositoryName, true),
        };

        if (keyword) {
            payload.keyword = keyword;
            payload.keywordType = 'tagName';
            payload.tagName = keyword;
        }

        return this.$http.getCCRCompanyRepositoryTags(payload).then(({result}) => {
            return result;
        });
    }

    // 获取用户、公共镜像版本
    getCCRRepositoryTags() {
        const {image, pager, keyword} = this.data.get();
        const {imageType, projectId, repositoryName} = image;
        const payload = {
            pageNo: pager.page,
            pageSize: pager.size,
            projectId,
            repositoryName,
        };

        if (keyword) {
            payload.keyword = keyword;
            payload.keywordType = 'name';
        }

        const url = imageType === 'userImage' ? 'getCCRRepositoryTags' : 'getPublicRepositoryTags';

        return this.$http[url](payload).then(({result}) => {
            const items = result.result.map(item => ({
                ...item,
                tagName: item.name,
            }));

            return {...result, items};
        });
    }

    onConfirm() {
        const selectedIndex = this.data.get('selection.selectedIndex');
        const datasource = this.data.get('table.datasource');
        const version = datasource[selectedIndex];
        this.data.set('open', false);
        this.fire('confirm', {...version});
    }
}
