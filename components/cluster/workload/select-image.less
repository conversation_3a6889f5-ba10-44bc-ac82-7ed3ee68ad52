.cce-workload-selectimage {
    .s-radio-button-group {
        margin-top: 12px;
    }

    .toolbar {
        margin: 20px 0 10px 0;
        display: flex;
        justify-content: flex-end;
        column-gap: 8px;
    }

    .s-radio-button-group {
        .s-radio-text {
            height: 30px;
            line-height: 30px;
            padding: 0 10px;
        }
    }
    .pagination {
        margin-top: 10px;

        .s-pagination {
            > .s-pagination-wrapper {
                justify-content: flex-end;
            }
        }
    }

    .s-table {
        .s-table-cell-repositoryName, .s-table-cell-address {
            .s-table-cell-text {
                word-break: break-all;
            }
        }
    }

    .s-tabs {
        margin-top: 8px;
    }
}
