/**
 * 侧边栏
 *
 * @file sidebar.js
 * <AUTHOR>
 */
import {Component} from 'san';
import {decorators, html, ServiceFactory, redirect} from '@baiducloud/runtime';
import {OutlinedLink} from '@baidu/sui-icon';
import {AppSidebar} from '@baidu/sui-biz';
const {invokeAppComp, invokeBceSanUI, asSidebar} = decorators;
const $flag = ServiceFactory.resolve('$flag');
export const BEC_REGION = 'edge';
@asSidebar()
@invokeAppComp
@invokeBceSanUI
class Sidebar extends Component {
    static template = html`
        <s-sidebar
            title="容器引擎CCE"
            active-name="{=active=}"
            hasFold="{{true}}"
            class="cce-sidebar"
        >
            <s-sidebar-item title="概览" name="cce-overview" link-to="/cce/overview" />
            <s-sidebar-item title="集群管理">
                <s-sidebar-item
                    title="集群列表"
                    name="cce-cluster-list"
                    link-to="/cce/cluster/list"
                />
                <s-sidebar-item
                    visible="{{!!$flag.CceClusterSnapshot}}"
                    title="集群快照"
                    name="cce-cluster-snapshot"
                    link-to="/cce/cluster/snapshot/list"
                />
            </s-sidebar-item>
            <s-sidebar-item title="安全管理">
                <s-sidebar-item
                    title="权限管理"
                    name="cce-cluster-rbac"
                    link-to="/cce/cluster/rbac"
                />
                <s-sidebar-item
                    title="角色"
                    name="cce-cluster-rbac-role"
                    link-to="/cce/cluster/rbac-role"
                />
            </s-sidebar-item>
            <s-sidebar-item title="Helm" name="cce-helm">
                <s-sidebar-item
                    title="Helm实例"
                    name="cce-helm-instance"
                    link-to="/cce/helm/instance/list"
                />
                <s-sidebar-item
                    title="Helm模板"
                    name="cce-helm-template"
                    link-to="/cce/helm/template/list"
                />
            </s-sidebar-item>
            <!--<s-sidebar-item visible="{{!$flag.CceSupportXS}}" title="镜像仓库" name="cce-image">
                <s-sidebar-item title="镜像列表" name="cce-image-list" link-to="/cce/image/list" />
                <s-sidebar-item title="命名空间" name="cce-namespace" link-to="/cce/space/list" />
            </s-sidebar-item>-->
            <s-sidebar-item
                title="备份中心"
                name="cce-backup-center"
                visible="{{BEC_REGION !== region}}"
            >
                <s-sidebar-item
                    title="备份仓库"
                    name="cce-backup-list"
                    link-to="/cce/backup/list"
                />
            </s-sidebar-item>
            <s-sidebar-item title="容器镜像服务CCR">
                <a
                    class="link-item"
                    href="/ccr/#/ccr/company/instance/list"
                    target="{{$flag.CceSupportXS ? '_self' : '_blank'}}"
                    slot="title"
                >
                    容器镜像服务CCR<s-link-icon class="link-icon" name="link" />
                </a>
            </s-sidebar-item>
            <s-sidebar-item visible="{{!$flag.CceSupportXS}}" title="Prometheus监控">
                <a class="link-item" href="/cprom/#/instance/list" target="_blank" slot="title">
                    Prometheus监控<s-link-icon class="link-icon" name="link" />
                </a>
            </s-sidebar-item>
            <s-sidebar-item visible="{{!$flag.CceSupportXS}}" title="服务网格CSM">
                <a class="link-item" href="/csm/" target="_blank" slot="title">
                    服务网格CSM<s-link-icon class="link-icon" name="link" />
                </a>
            </s-sidebar-item>
        </s-sidebar>
    `;
    static components = {
        's-link-icon': OutlinedLink,
        's-sidebar': AppSidebar,
        's-sidebar-item': AppSidebar.Item,
    };
    initData() {
        return {$flag, BEC_REGION, region: $context.getCurrentRegion().id};
    }

    inited() {
        this.watch('active', name => this.fire('active', {name}));
        this.data.set('region', $context.getCurrentRegion().id);
    }

    attached() {
        window.$framework.events.on('after_region_changed', e => {
            this.data.set('region', e.data);
            if (/^\#\/cce\/backup\//.test(location.hash) && BEC_REGION === e.data) {
                redirect('#/cce/cluster/list');
            }
        });
    }
}
