/**
 * client.js
 *
 * @file client.js
 * <AUTHOR>
 */
/* global $context */

import _ from 'lodash';
import {decorators, HttpClient, ServiceFactory} from '@baiducloud/runtime';

import {queryString, queryPodString} from './pager';
import {clusterListTransformV2toV1} from './v1-to-v2';
import codePlugin from './plugins/code';
import {checkClusterV2} from './service';

let BCC_FLAVOR_LIST_DATA = null;
let BBC_INSTANCE_LIST_DATA = null;
let VPC_LIST_DATA = null;
let BCC_IMAGE_LIST_DATA = null;

const CCEMONITORURL = '/api/cce/monitor';
const CCEAPPURL = '/api/cce/app';
const CCESERVICEURL = '/api/cce/service';
const CCEIMAGEURL = '/api/cce/image';
const CCECICDURL = '/api/cce/cis';
const CCEHELMURL = '/api/cce/helm';
const NETWORK = '/api/network';
const CCEHELMRELEASEURL = `${CCEHELMURL}/release`;
const CCEHELMCHARTURL = `${CCEHELMURL}/charts`;
const CCEVKURL = `${CCESERVICEURL}/v1/vk`;
const BBC_BASE = '/api/bbc';
const BEC_SERVICE_GROUP_URL = '/api/bec/servicegroup';
const BATCH_DEPLOYMENTS = '/v1/batchdeployments/namespace';
const SERVICE_GROUP_ACCESS = '/v1/services/namespace';
const CCEAIQUEUE = '/api/cce/service/v2/cluster';
const CCEAIQUEUE_V1 = '/api/cce/ai-service/v1/cluster';
const PFS_INSTANCE = '/api/pfs/instance';
// bls
const v2Api = '/api/bls/v2';
const v2BlsLogUrl = v2Api + '/log';
const v3BlsLogUrl = '/api/bls/v3/log';

// CCR
const CCR_COMPANY_SERVICE = '/api/ccr/esvc/v1';
const CCR_SERVICE = '/api/ccr/service/v1';

const CommonOptions = {'x-silent': true};

const ClusterNotFoundOptions = {
    'x-silent-codes': ['ClusterNotFound'],
};

const GlobalResources = ['storageclass', 'persistentvolume'];
const isGlobalResource = resource => _.includes(GlobalResources, resource);
const $flag = ServiceFactory.resolve('$flag');

@decorators.asService('$http')
class CceClient extends HttpClient {
    constructor(config = {}) {
        if ($flag.template === 'private-console') {
            super({CSRFToken: false}, $context);
        } else {
            super(config, $context);

            let plugin = [];
            if (this.responsePlugins) {
                plugin = this.responsePlugins;
            } else if (this.res && this.res.stack) {
                plugin = this.res.stack;
            }
            plugin.splice(1, 0, codePlugin(this));
        }
    }

    initMethods() {
        const methods = ['get', 'put', 'post', 'delete'];
        methods.forEach(method => {
            this[method] = (...args) =>
                super[method](...args).then(data => {
                    if (data && (data.success === false || data.success === 'false')) {
                        return Promise.reject(data);
                    }

                    return Promise.resolve(data);
                });
        });
    }

    getVKList(payload) {
        return this.get(`${CCEVKURL}/list`, payload);
    }

    deleteVK(payload, options = {}) {
        return this.delete(CCEVKURL, payload);
    }

    checkStsRole(params, options) {
        return this.post('/api/iam/sts/role/query', params, {headers: options});
    }

    checkStsRoleV2(params) {
        return this.post('/api/iam/sts/role/queryV2', params);
    }

    deActivateStsRole(params) {
        return this.post('/api/iam/sts/role/deactivate', params);
    }

    getOverview(region, disablePod, options = {}) {
        options.headers = {region};
        const params = {};
        if (disablePod) {
            params.disablePod = true;
        }
        return this.get(`${CCEMONITORURL}/overview`, params, options);
    }

    getOverviewV2() {
        return this.get('/api/cce/monitor/overview/list');
    }

    getBbcDeploysetAvailableList(param, options = {}) {
        return this.post(`${BBC_BASE}/deployset/available`, param, options);
    }

    getBccDeploysetAvailableList(param, options = {}) {
        return this.post('/api/bcc/deployset/available', param, options);
    }

    createBccDeployset(param, options = {}) {
        return this.post('/api/bcc/deployset', param, options);
    }

    createBbcDeployset(param, options = {}) {
        return this.post(`${BBC_BASE}/deployset/create`, param, options);
    }

    getBCCKeypairQuota(param, options = {}) {
        return this.post('/api/bcc/instance/quota', param, options);
    }

    getBCCKeypairList(param, options = {}) {
        return this.post('/api/bcc/keypair/list', param, options);
    }

    getSnapshotList(params = {}, options = {}) {
        // 快照列表查询
        return this.get(`${CCEMONITORURL}/backup/list`, params, options);
    }

    getSnapshotQuota(params = {}) {
        // 获取资源配额
        return this.get(`${CCEMONITORURL}/backup/quota`, params);
    }

    downloadSnapshot(params) {
        // 下载备份内容
        return this.get(`${CCEMONITORURL}/backup/download/${params.backupID}`);
    }

    updateSnapshot(params, options = {}) {
        // 更新备份内容
        return this.post(`${CCEMONITORURL}/backup/update`, params, options);
    }

    deleteSnapshot(params, options = {}) {
        // 批量删除备份内容
        return this.post(`${CCEMONITORURL}/backup/delete`, params, options);
    }

    createSnapshot(params, options = {}) {
        // 创建快照
        return this.post(`${CCEMONITORURL}/backup/create`, params, options);
    }

    checkRbacPermission(params) {
        return this.get(`${CCESERVICEURL}/v2/rbac/clusters`, params);
    }

    checkRoleValid(name) {
        return this.post('/api/iam/sts/role/list', {keyword: name});
    }

    getUserList() {
        return this.get(`${CCESERVICEURL}/v1/rbac/user/list`);
    }

    getRbacListById(userId) {
        return this.get(`${CCESERVICEURL}/v1/rbac/list/${userId}`);
    }

    getRbacListByIdV2(userId) {
        return this.get(`${CCESERVICEURL}/v2/rbac`, {userID: userId});
    }

    // 获取Bbc可用区列表
    getBbcZoneList(param, options = {}) {
        return this.post('/api/zone/list', param, options);
    }

    // 获取BBC 镜像列表
    getBbcFlavorImageList(param, options = {}) {
        return this.post(`${BBC_BASE}/image/list/flavors`, param, options);
    }

    // 获取BBC 自定义镜像列表
    getBbcFlavorCustomImageList(param, options = {}) {
        return this.post(`${BBC_BASE}/image/list/customFlavors`, param, options);
    }

    getBbcFlavorSharingImageList(param, option = {}) {
        return this.post(`${BBC_BASE}/image/list/bbcSharingFlavors`, param, option);
    }

    // 获取BBC 白名单标签
    getBbcWhiteTags(param, options = {}) {
        return this.post(`${BBC_BASE}/instance/white_tags`, param, options);
    }

    // 获取BBC 实例flavor
    getBbcInstanceFlavor(param = {}, options = {}) {
        // 只缓存全量的
        const haveParams = Object.keys(param).length > 0;
        if (!haveParams && BBC_INSTANCE_LIST_DATA) {
            return Promise.resolve(BBC_INSTANCE_LIST_DATA);
        }
        return this.post(`${BBC_BASE}/order/flavor`, param, options).then(res => {
            !haveParams && (BBC_INSTANCE_LIST_DATA = res);
            return Promise.resolve(res);
        });
    }

    // 获取bbc实例配额
    bbcInstanceQuota(param, options = {}) {
        return this.post(`${BBC_BASE}/instance/quota`, param, options);
    }

    // 获取集群是否安装GPUManager组件
    getGpuManagerStatus(clusterUuid, param = {}, options = {}) {
        if (!clusterUuid) {
            return Promise.reject();
        }
        return this.get(
            `${CCEAIQUEUE_V1}/${clusterUuid}/misc/isGPUManagerAvailable`,
            param,
            options,
        );
    }

    // 集群详情
    getClusterDetail(clusterUuid, options = {}) {
        return this.get(`${CCESERVICEURL}/v1/cluster/${clusterUuid}`, {}, options);
    }

    getClusterDetailV2(clusterUuid, options = {}) {
        return this.get(`${CCESERVICEURL}/v2/cluster/${clusterUuid}`, {}, options);
    }

    // 集群详情 - 修改备注
    updateComment(clusterUuid, options = {}) {
        return this.put(`${CCESERVICEURL}/v1/cluster/comment?clusterUuid=${clusterUuid}`, options);
    }
    // 集群详情 - 删除集群
    releaseCluster(clusterUuid, params) {
        return this.delete(`${CCESERVICEURL}/v1/cluster/${clusterUuid}`, params);
    }
    // 集群详情 - 凭证信息
    kubeConfigDownload(clusterUuid, type, options = {}) {
        return this.get(`${CCESERVICEURL}/v2/kubeconfig/${clusterUuid}/${type}`, {}, options);
    }
    // 集群详情 - 下载临时凭证
    kubeConfigDownloadExpire(params) {
        return this.post(`${CCESERVICEURL}/v2/rbac`, params);
    }
    // 集群详情 - 更新凭证
    kubeConfigUpdate(params) {
        return this.put(`${CCESERVICEURL}/v2/rbac`, params);
    }

    // 集群详情 - 更新标签
    updateClusterTags(payload) {
        return this.post(`${CCESERVICEURL}/v2/tag/assign`, payload);
    }

    // 集群列表 - 下载
    downloadClusterList(params) {
        return this.get('/api/cce/service/v2/clusters/export', params);
    }

    // 获取 集群 json 数据
    getClusterDetailJsonData(clusterUuid) {
        return this.get(`${CCEAIQUEUE}/${clusterUuid}/crd`);
    }
    // 更新 集群 json 数据
    editClusterDetailJsonData(clusterUuid, value) {
        return this.put(`${CCEAIQUEUE}/${clusterUuid}/crd`, value);
    }
    // 获取 集群 worker 节点 json数据
    getWorkerJsonData(clusterUuid, cceInstanceID) {
        return this.get(`${CCEAIQUEUE}/${clusterUuid}/instance/${cceInstanceID}/crd`);
    }
    // 更新 集群 worker 加点 json 数据
    editWorkerJsonData(clusterUuid, value) {
        return this.put(`${CCEAIQUEUE}/${clusterUuid}/instance/crd`, value);
    }

    // 集群事件
    getEventsList(params = {}) {
        const {clusterUuid, pageNo, pageSize, orderBy, order, startTime, endTime} = params;
        return this.get(
            `${CCESERVICEURL}/v1/eventrecord/list?clusterUuid=${clusterUuid}&pageNo=${pageNo}&` +
                `pageSize=${pageSize}&orderBy=${orderBy}&order=${order}&startTime=${startTime}&endTime=${endTime}`,
        );
    }

    getPromethuesConfigmap(clusterUuid) {
        return this.get(
            `${CCEAPPURL}/_raw/configmap/namespace/kube-system/name/prometheus?clusterUuid=${clusterUuid}`,
        );
    }

    validatePromsql(params = {}, options = {}) {
        let url = `${CCEMONITORURL}/monitor/check_promsql?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params, options);
    }

    editPrometheusConfigMap(params = {}, options = {}) {
        let url = `${CCEAPPURL}/_raw/configmap/namespace/kube-system/name/prometheus?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params.content, options);
    }

    editAlertmanagerConfigMap(params = {}, options = {}) {
        let url =
            `${CCEAPPURL}/_raw/configmap/namespace/kube-system/name/alertmanager` +
            `?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params.content, options);
    }

    getAlertManagerConfigmap(clusterUuid) {
        return this.get(
            `${CCEAPPURL}/_raw/configmap/namespace/kube-system/name/alertmanager` +
                `?clusterUuid=${clusterUuid}`,
        );
    }

    getIamUserList(param) {
        return this.post('/api/bcm/notify/party/list', param);
    }

    getIamGroupList(param) {
        return this.post('/api/bcm/notify/group/list', param);
    }

    // 集群审计
    listClusterAuditLog(params, options = {}) {
        return this.post(
            `${CCEMONITORURL}/audit/log?clusterUuid=${params.clusterUuid}`,
            params,
            options,
        );
    }

    // 集群服务画像
    listClusterReport(params, options = {}) {
        return this.post(
            `${CCEMONITORURL}/clusterreport/report?clusterUuid=${params.clusterUuid}`,
            params,
            options,
        );
    }

    checkIfDeployedAudit(params) {
        return this.get(`${CCEMONITORURL}/audit/deploy?clusterUuid=${params.clusterUuid}`);
    }

    deployAuditLog(params) {
        return this.post(`${CCEMONITORURL}/audit/deploy?clusterUuid=${params.clusterUuid}`);
    }

    iamUserAuditFields(params) {
        return this.post('/api/iam/user/audit/fields', params);
    }

    // 应用管理SDK
    // namespace
    getClustersList(params = {}, options = {}) {
        // 集群列表
        params.pageNo = params.pageNo || 1;
        params.pageSize = params.pageSize || 10;
        params.order = params.order || 'desc';
        params.orderBy = params.orderBy || 'createTime';
        return this.get(`${CCESERVICEURL}/v2/cluster/list`, params, options);
    }
    // 集群创建失败重试
    clusterRetry(clusterID) {
        return this.put(`${CCEAIQUEUE}/${clusterID}/retry`);
    }
    // 实例创建失败重试
    instanceRetry(clusterID, cceInstanceID, actions) {
        return this.put(`${CCEAIQUEUE}/${clusterID}/instance/${cceInstanceID}/retry?${actions}`);
    }

    // 跳过重装操作系统系统
    skipRebuild(clusterID, cceInstanceID, params) {
        return this.put(`${CCEAIQUEUE}/${clusterID}/instance/${cceInstanceID}/skipStep`, params);
    }

    getNamespaceQuota(params = {}, options = {}) {
        return this.get(`${CCEIMAGEURL}/v1/image/namespace/quota?quota`, params, options);
    }

    getNamespaceList(params = {}, options) {
        return this.get(`${CCEIMAGEURL}/v1/image/namespace/list`, params, options);
    }

    getImageUser(params = {}, options = {}) {
        return this.get(`${CCEIMAGEURL}/v1/image/user`, params, options);
    }

    createNamespace(params = {}) {
        return this.post(`${CCEIMAGEURL}/v1/image/namespace`, params);
    }

    releaseNamesapce(params) {
        return this.delete(`${CCEIMAGEURL}/v1/image/namespaces`, params);
    }

    updateNotifyUrl(params) {
        return this.put(`${CCEIMAGEURL}/v1/image/namespace`, params);
    }

    createImageUser(params) {
        return this.post(`${CCEIMAGEURL}/v1/image/user`, params);
    }

    updateImageUserPwd(params) {
        return this.put(`${CCEIMAGEURL}/v1/image/user`, params);
    }

    getHelmUser() {
        return this.get(`${CCEHELMURL}/account`, {}, {'x-silent': true});
    }

    createHelmUser(params) {
        return this.post(`${CCEHELMURL}/account`, params);
    }

    updateHelmUserPwd(params) {
        return this.put(`${CCEHELMURL}/account`, params);
    }

    getCicdImages(params) {
        return this.get(`${CCECICDURL}/image`, params);
    }

    updateCicdImageComment(params) {
        return this.put(`${CCECICDURL}/image/${params.uuid}`, _.omit(params, 'uuid'));
    }

    createCicdImage(params) {
        return this.post(`${CCECICDURL}/image`, params);
    }

    getGithubAccounts(params) {
        return this.get(`${CCECICDURL}/account/GITHUB`, params);
    }

    deleteCicdImage(params) {
        return this.delete(`${CCECICDURL}/image/${params.uuid}`);
    }

    deleteLogRule(params = {}) {
        return this.delete(`${CCEMONITORURL}/v2/logrules/${params.id}`, _.omit(params, 'id'));
    }

    releaseCharts(params, options = {}) {
        const {clusterUuid, namespace} = params;
        return this.post(
            `${CCEHELMRELEASEURL}?namespace=${namespace}&clusterUuid=${clusterUuid}`,
            params,
            CommonOptions,
        );
    }

    getPublicChartsValues(params) {
        return this.get(`${CCEHELMCHARTURL}/${params.chartname}/${params.version}/values`, params);
    }

    getPrivateChartsDetail(params) {
        return this.get(`${CCEHELMCHARTURL}/private/${params.chartname}`, params, CommonOptions);
    }

    listDockerImages(params = {}) {
        return this.get(`${CCEIMAGEURL}/v1/image/dockerhub`, params);
    }

    listOfficialHubImages(params = {}) {
        return this.get(`${CCEIMAGEURL}/v1/image/officialhub`, params);
    }

    getPublicChartsDetail(params) {
        return this.get(`${CCEHELMCHARTURL}/${params.chartname}`, params);
    }

    getPrivateChartsValues(params) {
        return this.get(
            `${CCEHELMCHARTURL}/private/${params.chartname}/${params.version}/values`,
            params,
        );
    }

    deletePrivateChart(params) {
        let path = `${CCEHELMCHARTURL}/private/${params.chartname}`;
        path += params.version ? `/${params.version}` : '';
        return this.delete(path);
    }

    getPublicChartsReadme(params) {
        return this.get(`${CCEHELMCHARTURL}/${params.chartname}/${params.version}/readme`, params);
    }

    // 镜像版本
    listCICDImageTags(options = {}) {
        const {imageUuid} = options;
        options.pageNo = options.pageNo || 1;
        return this.get(`${CCECICDURL}/image/${imageUuid}/tag`, options);
    }

    deleteCICDImageTag(options = {}) {
        const {imageUUID, tagID} = options;
        return this.delete(`${CCECICDURL}/image/${imageUUID}/tag/${tagID}`, options);
    }

    // config log
    listConfigLogs(options = {}) {
        options.pageNo = options.pageNo || 1;
        return this.get(`${CCECICDURL}/image/${options.imageUuid}/build`, options);
    }

    // 镜像构建
    listCICDConfigs(options = {}) {
        options.pageNo = options.pageNo || 1;
        return this.get(`${CCECICDURL}/image/${options.imageUuid}/conf`, options);
    }

    deleteCICDConfig(options = {}) {
        return this.delete(
            `${CCECICDURL}/image/${options.imageUuid}` +
                `/conf/${options.configType}/${options.id}`,
            options,
        );
    }

    buildCICDConfig(options = {}) {
        return this.post(
            `${CCECICDURL}/image/${options.imageUuid}` +
                `/conf/${options.confType}/${options.confId}/instantBuild`,
        );
    }

    getCICDImageDetail(options = {}) {
        return this.get(`${CCECICDURL}/image/${options.imageUuid}`, options);
    }

    getConfigLogBuildDetail(options = {}) {
        const {imageUuid, buildId} = options;
        return this.get(`${CCECICDURL}/image/${imageUuid}/build/${buildId}/conf/meta`);
    }

    getConfigLog(options = {}) {
        const {imageUuid, buildId} = options;
        return this.get(`${CCECICDURL}/image/${imageUuid}/build/${buildId}/log`, options);
    }

    getCICDConfigDetail(options = {}) {
        return this.get(
            `${CCECICDURL}/image/${options.imageUuid}/conf/${options.confType}/${options.confId}/meta`,
        );
    }

    getGithubStatus(accountId) {
        return this.get(`${CCECICDURL}/account/GITHUB/${accountId}`);
    }

    getGithubRepos(accountId, options = {}) {
        return this.get(`${CCECICDURL}/account/GITHUB/${accountId}/repo`, options);
    }

    getGithubBranches(accountId, repo, options = {}) {
        return this.get(`${CCECICDURL}/account/GITHUB/${accountId}/repo/${repo}/branch`, options);
    }

    validateConfigName(params = {}) {
        const {imageUuid, name} = params;
        return this.post(
            `${CCECICDURL}/image/${imageUuid}/conf/validateName?imageUuid=${imageUuid}&name=${name}`,
            '',
            CommonOptions,
        );
    }

    getCICDConfigData(options = {}) {
        return this.get(
            `${CCECICDURL}/image/${options.imageUuid}/conf/${options.confType}/${options.confId}/data`,
        );
    }

    updateCICDConfig({imageUuid, configType, confId}, config, options = {}) {
        return this.put(`${CCECICDURL}/image/${imageUuid}/conf/${configType}/${confId}`, config);
    }

    createCICDConfig({imageUuid, configType}, config, options = {}) {
        return this.post(`${CCECICDURL}/image/${imageUuid}/conf/${configType}`, config);
    }

    listAppNamespace(options = {}, xhrOptions = {}) {
        options.pageNo = options.pageNo || 1;
        if (!options.clusterUuid) {
            return Promise.reject();
        }
        return this.get(
            `${CCEAPPURL}/namespace`,
            queryString({...options, pageSize: 5000}),
            xhrOptions,
        );
    }

    getAppServiceInfo(options = {}) {
        return this.get(
            `${CCEAPPURL}/service/${options.namespaceName}/${options.serviceName}`,
            options,
        );
    }

    listAppIngress(options = {}) {
        const {pageNo, namespaceName} = options;
        options.pageNo = pageNo || 1;
        const url =
            namespaceName !== 'all'
                ? `${CCEAPPURL}/ingress-all-class/${namespaceName}`
                : `${CCEAPPURL}/ingress-all-class`;
        return this.get(url, queryString(options));
    }

    getIngressClassList(options = {}) {
        return this.get(
            `${CCEAIQUEUE}/${options.clusterUuid}/plugin/cce-nginx-ingress-controller/ingress-class-list`,
        );
    }

    getIngressDetail(options = {}) {
        const {namespaceName, ingressName} = options;
        const url = `${CCEAPPURL}/_raw/ingress/namespace/${namespaceName}/name/${ingressName}`;
        return this.get(url, options);
    }

    editAppIngress(params = {}, options = {}) {
        const {clusterUuid, namespaceName, name} = params;
        const url = `${CCEAPPURL}/_raw/ingress/namespace/${namespaceName}/name/${name}?clusterUuid=${clusterUuid}`;
        return this.post(url, params.content, options);
    }

    deleteAppIngress(options = {}) {
        const {clusterUuid, namespaceName, ingressName} = options;
        const url =
            `${CCEAPPURL}/delete_raw/ingress/namespace` +
            `/${namespaceName}/name/${ingressName}?clusterUuid=${clusterUuid}`;
        return this.post(url);
    }

    listAppService(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url = ns && ns !== 'all' ? `${CCEAPPURL}/service/${ns}` : `${CCEAPPURL}/service`;
        return this.get(url, queryString(options));
    }

    deleteAppService(options = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/service/namespace/` +
            `${options.namespaceName}/name/${options.serviceName}?clusterUuid=${options.clusterUuid}`;
        return this.post(url, options);
    }

    // 编辑标签
    editTagsService(params = {}) {
        return this.put(`${CCESERVICEURL}/v2/instances/tags`, params);
    }

    editTagsV1Service(params = {}) {
        return this.post('/api/bcc/tag/assign', params);
    }

    getInstanceRevisionValue(params, options = {}) {
        return this.get(`${CCEHELMRELEASEURL}/${params.name}/values`, params, options);
    }

    getInstanceDetail(params) {
        return this.get(`${CCEHELMRELEASEURL}/${params.name}`, params);
    }

    updateRelease(params, options = {}) {
        return this.put(
            `${CCEHELMRELEASEURL}/${params.name}?` +
                `clusterUuid=${params.clusterUuid}&namespace=${params.namespace}`,
            params,
        );
    }

    rollbackRelease(params) {
        const {namespace, clusterUuid, name, revision} = params;
        return this.post(
            `${CCEHELMRELEASEURL}/${params.name}/rollback?namespace=${namespace}` +
                `&clusterUuid=${clusterUuid}&name=${name}&revision=${revision}`,
        );
    }

    deleteRelease(params) {
        return this.delete(`${CCEHELMRELEASEURL}/${params.name}`, params);
    }

    installHelm(params) {
        return this.post(`${CCEHELMURL}/tiller?clusterUuid=${params.clusterUuid}`, params);
    }

    listReleases(params, options = {}) {
        return this.get(`${CCEHELMRELEASEURL}`, params, CommonOptions);
    }

    listContainerMonitors(options = {}) {
        return this.get(`${CCEMONITORURL}/monitor/deploy`, queryString(options)).then(data => ({
            body: data,
        }));
    }

    judgeOutCluster(clusterUuid) {
        return this.get(`${CCEMONITORURL}/monitor/cluster_check?clusterUuid=${clusterUuid}`);
    }

    deleteOldCluster(clusterUuid) {
        return this.post(`${CCEMONITORURL}/monitor/old_monitor_delete?clusterUuid=${clusterUuid}`);
    }

    checkAlertManager(clusterUuid) {
        return this.get(`${CCEMONITORURL}/monitor/check_alert?clusterUuid=${clusterUuid}`);
    }

    deployComponent(params, options = {}) {
        // options.body = JSON.stringify(params);
        return this.post(
            `${CCEMONITORURL}/monitor/deploy?clusterUuid=${params.clusterUuid}`,
            params,
            options,
        );
    }

    deleteComponent(params, options = {}) {
        options.data = JSON.stringify(params);
        return this.delete(
            `${CCEMONITORURL}/monitor/deploy?clusterUuid=${params.clusterUuid}`,
            '',
            options,
        );
    }

    unbindBlbService(params, options = {}) {
        options.data = JSON.stringify(params);
        return this.delete(
            `${CCEMONITORURL}/monitor/blb_service?clusterUuid=${params.clusterUuid}`,
            '',
            options,
        );
    }

    bindBlbService(params, options = {}) {
        options.data = JSON.stringify(params);
        return this.post(
            `${CCEMONITORURL}/monitor/blb_service?clusterUuid=${params.clusterUuid}`,
            '',
            options,
        );
    }

    bccCdsDiskList(params) {
        return this.post('/api/bcc/disk/list', params);
    }

    listNodes(options = {}) {
        options.pageNo = options.pageNo || 1;
        return this.get(`${CCESERVICEURL}/v1/node`, options);
    }

    listNodesV2(options = {}) {
        const clusterUuid = options.clusterUuid;
        options.pageNo = options.pageNo || 1;
        return this.get(
            `${CCESERVICEURL}/v2/cluster/${clusterUuid}/instances`,
            _.omit(options, 'clusterUuid'),
        );
    }

    downloadNode(clusterUuid, params) {
        return this.post(`/api/cce/service/v2/cluster/${clusterUuid}/instances/download`, params);
    }

    appblbPolicyList(params) {
        const url = '/api/blb/appblb/policy/list';
        return this.post(url, params);
    }

    certCreate(params) {
        const url = '/api/iam/certificate/create';
        return this.post(url, params);
    }

    getWorkflowLabel(clusterUuid, workflowtype, namesapceName) {
        return this.get(`${CCEAPPURL}/${workflowtype}/${namesapceName}`, {clusterUuid});
    }

    createServive(params = {}, options = {}) {
        let body = {
            name: '',
            namespace: params.namespace,
            content: params.content,
            validate: true,
        };
        return this.post(
            `${CCEAPPURL}/appservicefromfile?clusterUuid=${params.clusterUuid}`,
            body,
            options,
        );
    }

    editService(query = {}, params = {}) {
        return this.post(
            `${CCEAPPURL}/_raw/service/namespace/${query.namespace}/name/${query.serviceName}?clusterUuid=${query.clusterUuid}`,
            params,
        );
    }

    getService(params) {
        let url = `${CCEAPPURL}/_raw/service/namespace/${params.namespaceName}/name/${params.serviceName}`;
        return this.get(url, params);
    }

    createAppService(params = {}, options = {}) {
        let body = {
            name: '',
            namespace: params.namespaceName,
            content: params.content,
            validate: true,
        };
        return this.post(
            `${CCEAPPURL}/appdeploymentfromfile?clusterUuid=${params.clusterUuid}`,
            body,
            options,
        );
    }

    createIngressController(params = {}) {
        const url = `${CCEAPPURL}/deploy/ingresscontroller?clusterUuid=${params.clusterUuid}`;
        return this.post(url, {});
    }

    v3Price(params) {
        return this.post('/api/price/v3/order', params);
    }

    blbOrderPrice(params) {
        const url = '/api/blb/order/price';
        return this.post(url, params);
    }

    eipOrderPrice(params) {
        const url = '/api/eip/price';
        return this.post(url, params);
    }

    listAppNamespacePager(options = {}) {
        options.pageNo = options.pageNo || 1;
        return this.get(`${CCEAPPURL}/namespace`, queryString(options));
    }

    deleteAppNamespace(params = {}) {
        const {clusterUuid, namespaceName} = params;
        let url = `${CCEAPPURL}/delete_raw/namespace/name/${namespaceName}?clusterUuid=${clusterUuid}`;
        return this.post(url, params);
    }

    getNamespaceInfo(options = {}) {
        return this.get(`${CCEAPPURL}/namespace/${options.namespace}`, options);
    }

    editAppNamespace(params = {}, options = {}) {
        let obj = {
            apiVersion: 'v1',
            kind: 'Namespace',
            metadata: {
                name: params.namespaceName,
                labels: params.variables.reduce(function (map, obj) {
                    map[obj.name] = obj.value;
                    return map;
                }, {}),
            },
        };
        // options.body = JSON.stringify(obj);
        let url =
            `${CCEAPPURL}/_raw/namespace/name/` +
            `${params.namespaceName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, obj);
    }

    getMonitorMasterList(params = {}, options = {}) {
        let url = `${CCEMONITORURL}/clustermaster`;
        return this.get(url, params);
    }

    getMonitorMasterStatus(params = {}, options = {}) {
        let url = `${CCEMONITORURL}/clusterstatus`;
        return this.get(url, params);
    }

    getMonitorNodeStatus(params = {}, options = {}) {
        let url = `${CCEMONITORURL}/clusterslavecount`;
        return this.get(url, params);
    }

    editLogRule(data = {}, options = {}) {
        return this.put(
            `${CCEMONITORURL}/v2/logrules/${data.id}` +
                `?clusterUuid=${data.clusterUuid}&namespace=${data.namespace}`,
            _.omit(data, 'id', 'namespace', 'clusterUuid'),
            options,
        );
    }

    bcmMetricDataMetricName(params) {
        const url = '/api/bcm/metricdata/v2/datas/metricname';
        return this.post(url, params);
    }

    bosBucketList(params) {
        const url = '/api/cce/service/v2/backuprepositorys/buckets';
        return this.get(url, params);
    }

    getBosBucketDetail(params) {
        const url = '/api/bos/bucket/detail';
        return this.post(url, params);
    }

    createLogRule(data = {}, options = {}) {
        return this.post(
            `${CCEMONITORURL}/v2/logrules?clusterUuid=${data.clusterUuid}`,
            _.omit(data, 'clusterUuid'),
            options,
        );
    }

    appblbInstanceList(params) {
        const url = '/api/blb/appblb/instance/list';
        return this.post(url, params);
    }

    certList(params) {
        const url = '/api/iam/certificate/list';
        return this.post(url, params);
    }

    listLogRule(params) {
        return this.get(`${CCEMONITORURL}/v2/logrules`, params);
    }

    // 弱密码检测
    checkWeakPassword(payload, options = {}) {
        return this.post('/api/bcc/instance/checkWeakPassword', payload, options);
    }

    // cronjob
    listAppCronJob(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url = ns !== 'all' ? `${CCEAPPURL}/cronjob/${ns}` : `${CCEAPPURL}/cronjob`;
        return this.get(url, queryString(options));
    }
    // cronjob-servicegroup
    listAppCronJobServiceGroup(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url =
            ns !== 'all'
                ? `${BEC_SERVICE_GROUP_URL}/v1/cronjob/${ns}`
                : `${BEC_SERVICE_GROUP_URL}/v1/cronjob`;
        return this.get(url, queryString(options));
    }

    // DaemonSet
    listAppDaemonSet(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url = ns !== 'all' ? `${CCEAPPURL}/daemonset/${ns}` : `${CCEAPPURL}/daemonset`;
        return this.get(url, queryString(options));
    }

    // StatefulSet
    listAppStatefulSet(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url = ns !== 'all' ? `${CCEAPPURL}/statefulset/${ns}` : `${CCEAPPURL}/statefulset`;
        return this.get(url, queryString(options));
    }

    // secret
    listAppSecret(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url = ns !== 'all' ? `${CCEAPPURL}/secret/${ns}` : `${CCEAPPURL}/secret`;
        return this.get(url, queryString(options));
    }

    // new secret
    getSecretList(params) {
        const url =
            params.namespace !== 'all'
                ? `${CCEAPPURL}/secret/${params.namespace}`
                : `${CCEAPPURL}/secret`;
        return this.get(url, _.omit(params, 'namespace'));
    }

    createSecret(params) {
        return this.post(
            `/api/cce/app/appdeploymentfromfile?clusterUuid=${params.clusterUuid}`,
            _.omit(params, 'clusterUuid'),
        );
    }

    getSecretData(params) {
        const {namespace, name, clusterUuid} = params;
        return this.get(
            `/api/cce/app/_raw/secret/namespace/${namespace}/name/${name}?clusterUuid=${clusterUuid}`,
        );
    }

    editSecretYaml(params = {}) {
        const {namespace, name, clusterUuid, content} = params;
        let url = `${CCEAPPURL}/_raw/secret/namespace/${namespace}/name/${name}?clusterUuid=${clusterUuid}`;
        return this.post(url, content);
    }

    // deployment
    listAppDeployment(options = {}) {
        options.pageNo = options.pageNo || 1;
        const url =
            options.namespaceName !== 'all'
                ? `${CCEAPPURL}/deployment/${options.namespaceName}`
                : `${CCEAPPURL}/deployment`;
        return this.get(url, queryString(options));
    }
    // deployment-servicegroup
    listAppDeploymentServiceGroup(options = {}) {
        options.pageNo = options.pageNo || 1;
        const url =
            options.namespaceName !== 'all'
                ? `${BEC_SERVICE_GROUP_URL}/v1/deployment/${options.namespaceName}`
                : `${BEC_SERVICE_GROUP_URL}/v1/deployment`;
        return this.get(url, queryString(options));
    }

    // pod
    listAppPod(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url = ns !== 'all' ? `${CCEAPPURL}/pod/${ns}` : `${CCEAPPURL}/pod`;
        const params = queryPodString(options);
        if (options.filterBy) {
            params.filterBy = options.filterBy;
        }
        return this.get(url, params);
    }
    // pod-servicegroup
    listAppPodServiceGroup(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url =
            ns !== 'all'
                ? `${BEC_SERVICE_GROUP_URL}/v1/pod/${ns}`
                : `${BEC_SERVICE_GROUP_URL}/v1/pod`;
        return this.get(url, queryString(options));
    }

    // job
    listAppJob(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url = ns !== 'all' ? `${CCEAPPURL}/job/${ns}` : `${CCEAPPURL}/job`;
        return this.get(url, queryString(options));
    }
    // job-servicegroup
    listAppJobServiceGroup(options = {}) {
        options.pageNo = options.pageNo || 1;
        const ns = options.namespaceName;
        const url =
            ns !== 'all'
                ? `${BEC_SERVICE_GROUP_URL}/v1/job/${ns}`
                : `${BEC_SERVICE_GROUP_URL}/v1/job`;
        return this.get(url, queryString(options));
    }

    toggleEvent(clusterUuid, enable) {
        const path = enable ? 'open' : 'close';
        return this.put(`${CCEMONITORURL}/event/${path}?clusterUuid=${clusterUuid}`);
    }

    toggleEventPush(clusterID, enable) {
        return this.post(`${CCESERVICEURL}/v2/cluster/${clusterID}/bcm?enable=${enable}`);
    }

    getEventPushStatus(clusterID) {
        return this.get(`${CCESERVICEURL}/v2/cluster/${clusterID}/bcm`);
    }

    getEventStatus(clusterUuid) {
        return this.get(`${CCEMONITORURL}/event/status?clusterUuid=${clusterUuid}`);
    }

    getEvent(params, options = {}) {
        return this.post(
            `${CCEMONITORURL}/event?clusterUuid=${params.clusterUuid}`,
            params,
            options,
        );
    }

    createRbac(params, options = {}) {
        return this.post(`${CCESERVICEURL}/v1/rbac`, params, options);
    }

    createRbacV2(params, options = {}) {
        return this.post(`${CCESERVICEURL}/v2/rbac`, params, options);
    }

    deleteRbac(params, options = {}) {
        options.data = JSON.stringify(params);
        return this.delete(`${CCESERVICEURL}/v1/rbac`, params, options);
    }

    deleteRbacV2(params, options = {}) {
        options.data = JSON.stringify(params);
        return this.delete(`${CCESERVICEURL}/v2/rbac`, params, options);
    }

    getLogRule(params = {}) {
        return this.get(`${CCEMONITORURL}/v2/logrules/${params.ruleId}`, _.omit(params, 'ruleId'));
    }

    getMonitorNodeList(params = {}, options = {}) {
        return this.get(`${CCEMONITORURL}/clusterslave`, params, _.extend(options, CommonOptions));
    }

    checkK8sVersion(params = {}) {
        return this.get(`${CCESERVICEURL}/v1/cluster/cluster_upgrade/check/${params.clusterUuid}`);
    }

    getClusterHealthyStatus(params = {}) {
        return this.get(`${CCESERVICEURL}/v2/healthcheck/cluster/${params.clusterUuid}`);
    }

    getInstanceHealthyStatus(params = {}) {
        return this.get(
            `${CCESERVICEURL}/v2/healthcheck/instance/${params.clusterUuid}/${params.cceInstanceID}?isVKNode=${params.isVKNode}`,
        );
    }

    getAppDeploymentInfo(params = {}, options = {}) {
        return this.get(
            `${CCEAPPURL}/deployment/${params.namespaceName}/${params.deploymentName}`,
            params,
            options,
        );
    }
    // deployment-info-service-group
    getAppDeploymentInfoServiceGroup(params = {}, options = {}) {
        return this.get(
            `${BEC_SERVICE_GROUP_URL}/v1/deployment/${params.namespaceName}/${params.deploymentName}`,
            params,
            options,
        );
    }

    deleteAppDeployment(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/deployment/namespace/` +
            `${params.namespaceName}/name/${params.deploymentName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }

    scaleAppDeployment(params = {}) {
        params.scaleBy = parseInt(params.scaleNumber, 10);
        let url =
            `${CCEAPPURL}/scale/deployment/${params.namespaceName}/` +
            `${params.deploymentName}?clusterUuid=${params.clusterUuid}&scaleBy=${params.scaleNumber}`;
        return this.post(url, params);
    }

    reloadAppDeployment(params = {}) {
        let url =
            `${CCEAPPURL}/reload/deployment/${params.namespaceName}/` +
            `${params.deploymentName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }

    updateK8sVersion(params = {}) {
        return this.post(`${CCESERVICEURL}/v1/cluster/cluster_upgrade/upgrade`, params);
    }

    getGroupUpgradeVersion(clusterId, instancegroupId) {
        return this.get(
            `/api/cce/service/v2/cluster/${clusterId}/instancegroup/${instancegroupId}/component_upgrade_versions`,
        );
    }

    // Helm
    getPublicCharts(params = {}) {
        return this.get(`${CCEHELMURL}/charts`, params);
    }

    getPrivateCharts(params = {}) {
        return this.get(`${CCEHELMURL}/charts/private`, params, CommonOptions);
    }

    getInsallPublicData(params = {}) {
        let url = `/api/cce/helm/charts/${params.chartName}/${params.version}/values`;
        return this.get(url, params);
    }

    getInsallPrivateData(params = {}) {
        let url = `/api/cce/helm/charts/private/${params.chartName}/${params.version}/values`;
        return this.get(url, params);
    }

    checkPrivateTemplateName({chartname, version}) {
        return this.get(`/api/cce/helm/charts/private/${chartname}/${version}/check`);
    }

    // zone
    getZoneListV3(params = {}) {
        return this.post('/api/zone/list/v3', params);
    }

    // 安全组
    getInstanceList(params = {}) {
        return this.post('/api/network/v1/security/list', params);
    }

    // 企业安全组， from BCC 项目
    getEnterpriseSecurityGroupList() {
        return this.post('/api/network/v1/enterprise/security/list');
    }

    // 获取安全组详情
    getSecurityDetail(id) {
        return this.post(`${NETWORK}/v1/security/detail`, {
            id,
        });
    }

    // 校验子网
    checkVKNet(params = {}) {
        return this.post('/api/cce/service/v1/vk/check_vk_net', params);
    }

    // 提交数据
    createVK(params = {}) {
        return this.post('/api/cce/service/v1/vk', params);
    }
    getDeploymentMonitor(params = {}) {
        return this.get(`${CCEMONITORURL}/metric/resource`, params);
    }

    deleteAppPod(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/pod/namespace/` +
            `${params.namespaceName}/name/${params.podName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }
    getAppPodInfo(params = {}) {
        return this.get(`${CCEAPPURL}/pod/${params.namespaceName}/${params.podName}`, params);
    }
    getAppPodContainerLog(params = {}) {
        const url = `${CCEAPPURL}/log/${params.namespaceName}/${params.podName}/${params.containerName}`;
        return this.get(url, params);
    }
    getWebSSHToken(params = {}) {
        const url =
            `${CCEAPPURL}/containerwebshell/launch/${params.namespace}/${params.podName}/` +
            `${params.containerName}?clusterUuid=${params.clusterUuid}&commandType=${params.commandType}`;
        return this.get(url, {}, CommonOptions);
    }

    deleteAppJob(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/job/namespace/` +
            `${params.namespaceName}/name/${params.jobName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }

    getAppJobInfo(params = {}) {
        return this.get(`${CCEAPPURL}/job/${params.namespaceName}/${params.jobName}`, params);
    }

    deleteAppCronjob(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/cronjob/namespace/` +
            `${params.namespaceName}/name/${params.cronjobName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }
    getAppCronjobInfo(params = {}) {
        return this.get(
            `${CCEAPPURL}/cronjob/${params.namespaceName}/${params.cronjobName}`,
            params,
        );
    }
    // cronjob-info-service-group
    getAppCronjobInfoServiceGroup(params = {}) {
        return this.get(
            `${BEC_SERVICE_GROUP_URL}/v1/cronjob/${params.namespaceName}/${params.cronjobName}`,
            params,
        );
    }

    // 守护进程
    listAppDaemonset(params = {}) {
        const ns = params.namespaceName;
        const url = ns !== 'all' ? `${CCEAPPURL}/daemonset/${ns}` : `${CCEAPPURL}/daemonset`;
        return this.get(url, queryString(params));
    }
    deleteAppDaemonset(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/daemonset/namespace/` +
            `${params.namespaceName}/name/${params.daemonsetName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }
    getAppDaemonsetInfo(params = {}) {
        return this.get(
            `${CCEAPPURL}/daemonset/${params.namespaceName}/${params.daemonsetName}`,
            params,
        );
    }
    // 有状态部署
    listAppStatefulset(params = {}) {
        params.pageNo = params.pageNo || 1;
        const ns = params.namespaceName;
        const url = ns !== 'all' ? `${CCEAPPURL}/statefulset/${ns}` : `${CCEAPPURL}/statefulset`;
        return this.get(url, queryString(params));
    }
    // 有状态部署-service-group
    listAppStatefulsetServiceGroup(params = {}) {
        params.pageNo = params.pageNo || 1;
        const ns = params.namespaceName;
        const url =
            ns !== 'all'
                ? `${BEC_SERVICE_GROUP_URL}/v1/statefulset/${ns}`
                : `${BEC_SERVICE_GROUP_URL}/v1/statefulset`;
        return this.get(url, queryString(params));
    }
    deleteAppStatefulset(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/statefulset/namespace/` +
            `${params.namespaceName}/name/${params.statefulsetName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }
    getAppStatefulsetInfo(params = {}) {
        return this.get(
            `${CCEAPPURL}/statefulset/${params.namespaceName}/${params.statefulsetName}`,
            params,
        );
    }
    // stateful-info-service-group
    getAppStatefulsetInfoServiceGroup(params = {}) {
        return this.get(
            `${BEC_SERVICE_GROUP_URL}/v1/statefulset/` +
                `${params.namespaceName}/${params.statefulsetName}`,
            params,
        );
    }
    // HPA
    getClusterHpa(params) {
        const ns = params.namespaceName;
        const url =
            ns !== 'all'
                ? `${CCEAPPURL}/horizontalpodautoscaler/${ns}`
                : `${CCEAPPURL}/horizontalpodautoscaler`;
        return this.get(url, queryString(params));
    }
    // HPA kind
    getClusterKindHpa(params) {
        const url = `${CCEAPPURL}/${params.kind}/${params.namespace}/${params.name}/horizontalpodautoscaler`;
        return this.get(url, queryString(_.omit(params, 'kind', 'name')));
    }
    // 获取指定name的hpa详情
    getHpaByName(params) {
        const url =
            `${CCEAPPURL}/_raw/horizontalpodautoscaler/namespace/` +
            `${params.namespace}/name/${params.hpaName}?clusterUuid=${params.clusterUuid}`;
        return this.get(url);
    }
    deleteHpa(params, options = {}) {
        const url =
            `${CCEAPPURL}/_raw/horizontalpodautoscaler/namespace/` +
            `${params.namespaceName}/name/${params.hpaName}?clusterUuid=${params.clusterUuid}`;
        return this.delete(url, {}, options);
    }
    updateHpa(params, config = {}) {
        const url =
            `${CCEAPPURL}/_raw/horizontalpodautoscaler/namespace/` +
            `${params.namespace}/name/${params.hpaName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, config);
    }
    createAppFromFile(params = {}, xhrOptions = {}) {
        let options = {
            name: '',
            namespace: params.namespaceName,
            content: params.content,
            validate: true,
        };
        return this.post(
            `${CCEAPPURL}/appdeploymentfromfile?clusterUuid=${params.clusterUuid}`,
            options,
            xhrOptions,
        );
    }

    // configmap
    listAppConfigMap(params = {}) {
        params.pageNo = params.pageNo || 1;
        const ns = params.namespaceName;
        const url = ns !== 'all' ? `${CCEAPPURL}/configmap/${ns}` : `${CCEAPPURL}/configmap`;
        return this.get(url, queryString(params));
    }

    deleteAppConfigMap(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/configmap/namespace/` +
            `${params.namespaceName}/name/${params.configmapName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }

    getAppConfigMapInfo(params = {}) {
        return this.get(
            `${CCEAPPURL}/configmap/${params.namespaceName}/${params.configmapName}`,
            params,
        );
    }

    deleteAppSecret(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/secret/namespace/` +
            `${params.namespaceName}/name/${params.secretName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }
    getAppSecretInfo(params = {}) {
        return this.get(`${CCEAPPURL}/secret/${params.namespaceName}/${params.secretName}`, params);
    }
    // storageclass
    listAppStorageClass(params = {}) {
        params.pageNo = params.pageNo || 1;
        return this.get(`${CCEAPPURL}/storageclass`, queryString(params));
    }
    deleteAppStorageClass(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/storageclass/name/` +
            `${params.storageclassName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }
    createAppStorageClass(params = {}) {
        let options = {
            name: '',
            namespace: params.namespaceName,
            content: params.content,
            validate: true,
        };
        return this.post(
            `${CCEAPPURL}/appdeploymentfromfile?clusterUuid=${params.clusterUuid}`,
            options,
        );
    }
    getAppStorageClassInfo(params = {}) {
        return this.get(`${CCEAPPURL}/storageclass/${params.storageclassName}`, params);
    }
    // pv
    listAppPV(params = {}) {
        params.pageNo = params.pageNo || 1;
        return this.get(`${CCEAPPURL}/persistentvolume`, params);
    }
    getAppPVYaml(params = {}) {
        let url = `${CCEAPPURL}/_raw/persistentvolume/name/${params.pvName}`;
        return this.get(url, params);
    }
    deleteAppPV(params = {}) {
        let url = `${CCEAPPURL}/delete_raw/persistentvolume/name/${params.pvName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }
    getAppPVInfo(params = {}) {
        return this.get(`${CCEAPPURL}/persistentvolume/${params.pvName}`, params);
    }
    // pvc
    listAppPVC(params = {}) {
        params.pageNo = params.pageNo || 1;
        const ns = params.namespaceName;
        const url =
            ns !== 'all'
                ? `${CCEAPPURL}/persistentvolumeclaim/${ns}`
                : `${CCEAPPURL}/persistentvolumeclaim`;
        return this.get(url, queryString(params));
    }
    getAppPVCYaml(params = {}) {
        let url = `${CCEAPPURL}/_raw/persistentvolumeclaim/namespace/${params.namespaceName}/name/${params.pvcName}`;
        return this.get(url, params);
    }

    detailCluster(clusterUuid, options = {}) {
        if (!clusterUuid) {
            return Promise.reject();
        }
        return this.get(`${CCESERVICEURL}/v1/cluster/${clusterUuid}`, options);
    }

    getNodesInfo(params = {}, region, options = CommonOptions) {
        if (region) {
            options.headers = {region};
        }
        return this.get(`${CCEAPPURL}/node`, queryString(params), options);
    }

    cordon(params) {
        return this.post(`${CCESERVICEURL}/v2/nodes/cordon`, params);
    }

    setGPUShare(params) {
        return this.post(`${CCESERVICEURL}/v2/nodes/gpu_share`, params);
    }

    checkEnableGpuShare(params) {
        const {clusterID, nodename} = params;
        return this.get(
            `/api/cce/ai-service/v1/cluster/${clusterID}/misc/enableGpuShare/${nodename}`,
        );
    }

    getClusterQuota() {
        return this.get(`${CCESERVICEURL}/v1/cluster/quota?quota`);
    }

    getClusterQuotaV2() {
        return this.get(`${CCESERVICEURL}/v2/quota/cluster`);
    }

    getRbacClusterList() {
        return this.get(`${CCESERVICEURL}/v1/rbac/cluster/list`);
    }

    getRbacClusterListV2(params = {}) {
        return this.get(`${CCESERVICEURL}/v2/rbac/clusters`, params);
    }

    getRbacNamespaceList(params = {}) {
        return this.get(`${CCESERVICEURL}/v1/rbac/namespace/list`, params);
    }

    getRbacNamespaceListV2(params = {}) {
        return this.get(`${CCESERVICEURL}/v2/rbac/namespaces`, params);
    }

    deleteAppPVC(params = {}) {
        let url =
            `${CCEAPPURL}/delete_raw/persistentvolumeclaim/namespace/` +
            `${params.namespaceName}/name/${params.pvcName}?clusterUuid=${params.clusterUuid}`;
        return this.post(url, params);
    }
    getAppPVCInfo(params = {}) {
        return this.get(
            `${CCEAPPURL}/persistentvolumeclaim/${params.namespaceName}/${params.pvcName}`,
            params,
        );
    }

    checkWhiteList(featureType) {
        return this.get(`${CCESERVICEURL}/v1/cluster/check_white_list?featureType=${featureType}`);
    }

    getKubernetesList() {
        return this.get(`${CCESERVICEURL}/v1/cluster/versions`);
    }

    getBecRegion() {
        return Promise.all([
            this.get('/api/bec/service/v1/node/type/vm?country=all'),
            this.get('/api/bec/service/v1/node/type/bm?country=all'),
        ]).then(([vm, bm]) => {
            return {
                success: vm?.success || bm?.success,
                result: {
                    regionList: [
                        ...(vm?.result?.regionList || []),
                        ...(bm?.result?.regionList || []),
                    ],
                },
            };
        });
    }

    getClusterFlavor(clusterUuid, options) {
        return this.get(`${CCESERVICEURL}/v2/cluster/${clusterUuid}/scale_info`, {}, options);
    }

    getVpcList(params = {}, refresh) {
        if (VPC_LIST_DATA && !refresh) {
            return Promise.resolve(VPC_LIST_DATA);
        }
        return this.post('/api/network/v1/vpcs', params).then(res => {
            VPC_LIST_DATA = res;
            return Promise.resolve(res);
        });
    }

    getEipList() {
        return this.post('/api/eip/list', {
            order: 'desc',
            orderBy: 'createTime',
            pageNo: 1,
            pageSize: 1000,
        });
    }

    getBecVpcList(regionId) {
        return this.get(`/api/bec/service/v1/vpc?regionId=${regionId}`);
    }

    getVpcDetail(vpcId) {
        return this.get(`${NETWORK}/v1/vpc/detail/${vpcId}`);
    }

    getClusterBLBList(vpcId) {
        return this.get(`${CCESERVICEURL}/v2/app_blb/${vpcId}`);
    }

    getSubnetList(params = {}) {
        return this.post('/api/network/v1/subnets', params);
    }

    getBecSubnetList({regionId, vpcId}) {
        return this.get(
            `/api/bec/service/v1/vpc/subnet?regionId=${regionId}&vpcId=${vpcId}&subnetType=common`,
        );
    }

    checkContainerNet(params = {}, options = {}) {
        return this.post(`${CCESERVICEURL}/v1/cluster/check_container_net`, params, options);
    }

    checkEniQuota(params = {}) {
        return this.get(`${CCESERVICEURL}/v1/node/check_eni_quota`, params);
    }

    getExclusiveServerList(params = {}) {
        return this.post('/api/bcc/dcc/host/list', params);
    }

    getDccDetail(params = {}) {
        return this.post('/api/bcc/dcc/host/detail', params);
    }

    accountPurchaseValidation(params = {}, options = {}) {
        return this.post('/api/account/purchase_validation', params, options);
    }

    checkInternalUser() {
        return this.get(`${CCESERVICEURL}/v1/cluster/check_internal_user`);
    }

    getAvailableContainerNet(params = {}, options = {}) {
        return this.post(`${CCESERVICEURL}/v1/cluster/get_container_net`, params);
    }

    getBccWhiteTags(params = {}) {
        return this.post('/api/bcc/instance/white_tags', params);
    }

    eipMaxBandQuota(params = {}) {
        return this.post('/api/eip/maxBandQuota', params, CommonOptions);
    }

    createDefaultSecurityGroup(params = {}) {
        return this.post(
            `${CCESERVICEURL}/v1/cluster/security_groups/node/create?vpcId=${params.vpcId}`,
        );
    }

    getSecurityListSelect(params = {}) {
        return this.post('/api/network/v1/security/list_select', params);
    }

    getBecSecurityList() {
        return this.get('/api/bec/service/v1/securityGroup');
    }

    checkSecurityGroupRule(params = {}) {
        const param = `vpcId=${params.vpcId}&securityGroupId=${params.securityGroupId}`;
        const url = `${CCESERVICEURL}/v1/cluster/security_groups/node/check?${param}`;
        return this.post(url, null, CommonOptions);
    }

    updateSecurityGroupRule(params = {}) {
        const param = `vpcId=${params.vpcId}&securityGroupId=${params.securityGroupId}`;
        const url = `${CCESERVICEURL}/v1/cluster/security_groups/node/update?${param}`;
        return this.put(url, null, CommonOptions);
    }

    sshList(params = {}, options = {}) {
        return this.post('/api/bcc/keypair/list', params, options);
    }

    updateAutoScalingConf(clusterUuid, autoScalingConf, options = {}) {
        const url = `${CCESERVICEURL}/v1/cluster/auto_scaling_conf?clusterUuid=${clusterUuid}`;
        return this.put(url, autoScalingConf, options);
    }

    create(params = {}, options = {}) {
        // 如果是集群id，走扩容接口。为什么不直接走expandCluster？
        // 集群创建、扩容后段对应2个不同接口，但都是走的billing定单确认，如果需要兼容，还要在订单页面写各种业务逻辑处理
        if (params.clusterUuid) {
            return this.expandCluster(params, options);
        }

        return this.post(`${CCESERVICEURL}/v1/order/confirm`, params, options);
    }

    expandCluster(params = {}, options = {}) {
        return this.post(`${CCESERVICEURL}/v1/cluster/scalingUp?scalingUp`, params, options);
    }

    bccInstancePriceV2(params = {}) {
        return this.post('/api/bcc/instance/priceV2', params, CommonOptions);
    }

    createAutoscalerGroup(params, options = {}) {
        return this.post(`${CCESERVICEURL}/v1/autoscaler/group`, params, options);
    }

    editAutoscalerGroup(params = {}, options = {}) {
        params = _.pick(params, ['clusterUuid', 'groupId', 'minNodeNum', 'maxNodeNum', 'priority']);
        return this.put(`${CCESERVICEURL}/v1/autoscaler/group`, params, options);
    }

    editAutoscalerGlobalConfig(params = {}, options = {}) {
        return this.put(`${CCESERVICEURL}/v1/autoscaler/global_config`, params, options);
    }

    getAutoscalerConfig(params = {}) {
        return this.get(`${CCESERVICEURL}/v1/autoscaler/configs`, params);
    }

    getAutoscalerGroup(params = {}) {
        return this.get(`${CCESERVICEURL}/v1/autoscaler/group/list`, params);
    }

    enableAutoscalerGroup(params = {}, options = {}) {
        params = _.pick(params, ['clusterUuid', 'groupId', 'groupEnable']);
        return this.put(`${CCESERVICEURL}/v1/autoscaler/group/status`, params, options);
    }

    deleteAutoscalerGroup(params = {}) {
        return this.delete(`${CCESERVICEURL}/v1/autoscaler/group`, params);
    }

    openAutoScaler(params = {}) {
        return this.put(`${CCESERVICEURL}/v1/autoscaler?clusterUuid=${params.clusterUuid}`);
    }

    // yaml操作
    getUserYaml(params = {}, options = {}) {
        if ($flag.CceApplicationYamlTemplateRegionBj) {
            options.headers = {region: 'bj'};
        }
        const url = `${CCEAPPURL}/yaml`;
        return this.get(url, params, options);
    }
    updateUserYamlById(params = {}, options = {}) {
        if ($flag.CceApplicationYamlTemplateRegionBj) {
            options.headers = {region: 'bj'};
        }
        const url = `${CCEAPPURL}/yaml`;
        return this.put(`${url}/${params.id}`, params, options);
    }
    deleteUserYamlById(params = {}, options = {}) {
        if ($flag.CceApplicationYamlTemplateRegionBj) {
            options.headers = {region: 'bj'};
        }
        const url = `${CCEAPPURL}/yaml`;
        return this.delete(`${url}/${params.id}`, null, options);
    }
    addUserYaml(params = {}, options = {}) {
        if ($flag.CceApplicationYamlTemplateRegionBj) {
            options.headers = {region: 'bj'};
        }
        const url = `${CCEAPPURL}/yaml`;
        return this.post(url, params, options);
    }
    getAppYaml(moduleName, params) {
        let url =
            `${CCEAPPURL}/_raw/${moduleName}/` +
            (isGlobalResource(moduleName) ? '' : `namespace/${params.namespaceName}/`) +
            `name/${params.name}`;
        return this.get(url, params);
    }
    editAppYaml(params = {}) {
        const {moduleName, namespaceName, name, clusterUuid, content} = params;
        let url =
            `${CCEAPPURL}/_raw/${moduleName}/` +
            (isGlobalResource(moduleName) ? '' : `namespace/${namespaceName}/`) +
            `name/${name}?clusterUuid=${clusterUuid}`;
        return this.post(url, content);
    }

    bbcInstanceName(params = {}) {
        return this.post('/api/bbc/instance/rename', params);
    }

    bccInstancePwd(params = {}) {
        return this.post('/api/bcc/instance/changepwd', params);
    }

    bccInstanceJoinSecurityGroups(params = {}) {
        return this.post('/api/network/v1/security/join_security_groups', params);
    }

    bccInstanceDetail(params = {}) {
        return this.post('/api/bcc/instance/detail', params);
    }

    bbcInstanceImage(params = {}) {
        return this.post('/api/bbc/instance/create_image', params);
    }

    bccInstanceImage(params = {}) {
        return this.post('/api/bcc/instance/create_image', params);
    }

    eipInstanceList(params = {}) {
        return this.post('/api/bcc/eip/unused_eips', params);
    }

    cceBindEipInstance(params = {}) {
        return this.post('/api/bcc/eip/bind', params);
    }

    cceBindEipV2Instance(params = {}) {
        return this.put(
            `${CCESERVICEURL}/v2/instance/${params.cceInstanceID}/eip?bind=&eip=${params.eip}`,
        );
    }
    cceUnBindEipV2Instance(params = {}) {
        return this.put(`${CCESERVICEURL}/v2/instance/${params.cceInstanceID}/eip?unbind=`);
    }

    vncUrl(params = {}) {
        return this.post('/api/bcc/instance/vnc', params);
    }

    bccSecurityGroups(params = {}) {
        return this.post('/api/bcc/vpc/security/securityGroups', params);
    }

    invalidateAuthCode(params = {}) {
        return this.post('/v1/authcode/invalidate', params);
    }

    getBccImageList(params = {}) {
        return this.post('/api/bcc/image/list', params);
    }

    getBccNodeListV2(params = {}) {
        return this.post(`${CCESERVICEURL}/v1/cluster/existed_bcc_node/list`, params);
    }

    getHpasNodeList(params = {}) {
        return this.post(`${CCESERVICEURL}/v2/cluster/existed_hpas_node/list`, params);
    }

    getBecNodeList(params = {}) {
        return this.get(`/api/bec/service/v1/vm/instance`, params);
    }

    getBecBmNodeList(params = {}) {
        return this.get('/api/bec/service/v1/bm', params);
    }

    getServiceConfig(configKey) {
        return this.get(`${CCESERVICEURL}/v2/available_config`, {configKey});
    }

    moveinNode(params = {}) {
        let url = `${CCESERVICEURL}/v1/cluster/existed_node?action=shift_in`;
        return this.post(url, params);
    }

    moveoutNode(params = {}) {
        // 新集群
        if (checkClusterV2(params.clusterUuid)) {
            const deleteIPDisk =
                params.deleteInstance && params.deleteIPDisk
                    ? {deleteResource: true, deleteCDSSnapshot: true}
                    : {};
            const drainNode =
                params.deleteOption && params.deleteOption.drainNode ? {drainNode: true} : {};
            const rebuild =
                params.deleteOption && params.deleteOption.rebuild ? {rebuild: true} : {};
            let payload = {
                instanceIDs: _.pluck(params.nodeInfoList, 'instanceId'),
                deleteOption: {
                    moveOut: !params.deleteInstance,
                    ...deleteIPDisk,
                    ...drainNode,
                    ...rebuild,
                },
            };
            if (params.scaleDown) {
                payload.scaleDown = params.scaleDown;
            }
            return this.put(`${CCESERVICEURL}/v2/cluster/${params.clusterUuid}/instances`, payload);
        }

        let url = `${CCESERVICEURL}/v1/cluster/existed_node?action=shift_out`;
        return this.post(url, params);
    }

    bbcInstanceList(params = {}) {
        return this.post('/api/bbc/instance/list', params, CommonOptions);
    }

    getSpecFlavorList(params = {}) {
        if (BCC_FLAVOR_LIST_DATA) {
            return Promise.resolve(BCC_FLAVOR_LIST_DATA);
        }
        return this.post('/api/bcc/instance/flavorSpec', params).then(res => {
            BCC_FLAVOR_LIST_DATA = res;
            return Promise.resolve(res);
        });
    }

    rebootInstance(params = {}) {
        return this.post('/api/bcc/instance/reboot', params);
    }

    stopInstance(params = {}) {
        return this.post('/api/bcc/instance/stop', params);
    }

    startInstance(params = {}) {
        return this.post('/api/bcc/instance/start', params);
    }

    bccInstanceName(params = {}) {
        return this.post('/api/bcc/instance/rename', params);
    }

    releaseNode(clusterUuid, options = {}) {
        if (checkClusterV2(clusterUuid)) {
            let payload = {
                instanceIDs: _.pluck(options.nodeInfo, 'instanceId'),
                deleteOption: {
                    deleteCDSSnapshot: options.deleteSnap,
                    deleteResource: options.deleteEipCds,
                    moveOut: false,
                },
            };
            return this.put(`${CCESERVICEURL}/v2/cluster/${clusterUuid}/instances`, payload);
        }
        let url =
            `${CCESERVICEURL}/v1/cluster/scalingDown?scalingDown=&from=console` +
            `&deleteEipCds=${options.deleteEipCds}&deleteSnap=${options.deleteSnap}`;
        return this.post(url, _.omit(options, 'deleteEipCds', 'deleteSnap'), options);
    }

    cceUnbindEipInstance(params = {}) {
        return this.post('/api/bcc/eip/unbind', params);
    }

    getResource(params = {}) {
        return this.get(`${CCEMONITORURL}/metric/resource`, params);
    }

    vpcMap(params = {}) {
        return this.post('/api/network/v1/vpcMap', params);
    }

    createRemedyrule(clusterID, params) {
        return this.post(`/api/cce/remedy/v1/clusters/${clusterID}/remedyrules`, params);
    }

    editRemedyrule(clusterID, ruleId, params) {
        return this.put(`/api/cce/remedy/v1/clusters/${clusterID}/remedyrules/${ruleId}`, params);
    }

    getRemedyruleList(params, options) {
        return this.get(
            `/api/cce/remedy/v1/clusters/${params.clusterUuid}/remedyrules`,
            params,
            options,
        );
    }

    getRemedyruleDetail(params) {
        return this.get(
            `/api/cce/remedy/v1/clusters/${params.clusterUuid}/remedyrules/${params.ruleId}`,
        );
    }

    deleteRemedyrule(params) {
        return this.delete(
            `/api/cce/remedy/v1/clusters/${params.clusterUuid}/remedyrules/${params.ruleId}`,
        );
    }

    groupBindRemedyrule(clusterID, instancegroupID, params) {
        return this.post(
            `/api/cce/remedy/v1/clusters/${clusterID}/instancegroups/${instancegroupID}/remediation`,
            params,
        );
    }

    remedyAuthorized(clusterID, instancegroupID, taskID) {
        return this.post(
            `/api/cce/remedy/v1/clusters/${clusterID}/instancegroups/${instancegroupID}/remedytasks/${taskID}/authRepair`,
            {
                isAuthorized: true,
            },
        );
    }

    remedyConfirmed(clusterID, instancegroupID, taskID) {
        return this.put(
            `/api/cce/remedy/v1/clusters/${clusterID}/instancegroups/${instancegroupID}/remedytasks/${taskID}/confirm`,
            {
                isConfirmed: true,
            },
        );
    }

    async getRemedyActiveTasks(clusterID, instancegroupID, params) {
        if (params.keyword) {
            const data = await this.get(
                `/api/cce/remedy/v1/clusters/${clusterID}/instancegroups/${instancegroupID}/remedytasks/${params.keyword}`,
                params,
            );
            const list = data?.result?.remedyTask ? [data.result.remedyTask] : [];
            return {
                result: {
                    remedyTaskPage: {
                        remedyTasks: list,
                        pageNo: 1,
                        pageSize: 10,
                        totalCount: 1,
                    },
                },
            };
        }
        return this.get(
            `/api/cce/remedy/v1/clusters/${clusterID}/instancegroups/${instancegroupID}/remedytasks`,
            params,
        );
    }

    async getRemedyActiveTask(clusterID, instancegroupID, taskID) {
        return this.get(
            `/api/cce/remedy/v1/clusters/${clusterID}/instancegroups/${instancegroupID}/remedytasks/${taskID}`,
        );
    }

    checkWebHook(clusterID, params) {
        return this.post(
            `/api/cce/remedy/v1/clusters/${clusterID}/remedyrules/webhook/check`,
            params,
        );
    }

    createSecurityGroupDefault(vpcId) {
        return this.post(`${CCESERVICEURL}/v2/security_group/${vpcId}/cce_default`);
    }

    checkMasterSecurityGroup(vpcId, securityGroupId) {
        return this.post(
            `${CCESERVICEURL}/v2/master_security_group/${vpcId}/${securityGroupId}/check`,
        );
    }

    checkNodeSecurityGroup(vpcId, securityGroupId) {
        return this.post(
            `${CCESERVICEURL}/v2/node_security_group/${vpcId}/${securityGroupId}/check`,
        );
    }

    updateMasterSecurityGroup(vpcId, securityGroupId) {
        return this.put(`${CCESERVICEURL}/v2/master_security_group/${vpcId}/${securityGroupId}`);
    }

    updateNodeSecurityGroup(vpcId, securityGroupId) {
        return this.put(`${CCESERVICEURL}/v2/node_security_group/${vpcId}/${securityGroupId}`);
    }

    addNodeInGroup(clusterUuid, groupId, params) {
        return this.put(
            `${CCESERVICEURL}/v2/cluster/${clusterUuid}/instancegroup/${groupId}/attachInstances`,
            params,
        );
    }

    queryBccPrice(params = {}) {
        return this.post(`${CCESERVICEURL}/v2/price/bcc`, params);
    }

    queryBlbPrice(params = {}) {
        return this.post(`${CCESERVICEURL}/v2/price/cluster`, params);
    }

    checkClusterIpCidr(params = {}, silent) {
        return this.post(
            `${CCESERVICEURL}/v2/net/check_clusterip_cidr`,
            params,
            silent ? silent : {},
        );
    }

    checkSubnetIpCidr(params = {}) {
        return this.post(`${CCESERVICEURL}/v2/net/check_auxiliaryip_cidr`, params, CommonOptions);
    }

    cceSecurityListSelect(params = {}) {
        return this.post('/api/network/v1/security/list_select', params);
    }

    recommendContainerCidr(params = {}) {
        return this.post(`${CCESERVICEURL}/v2/net/recommend_container_cidr`, params, CommonOptions);
    }

    recommendClusterIpCidr(params) {
        return this.post(`${CCESERVICEURL}/v2/net/recommend_clusterip_cidr`, params, CommonOptions);
    }

    checkContainerNetworkCidr(params = {}) {
        return this.post(`${CCESERVICEURL}/v2/net/check_container_network_cidr`, params);
    }

    createExistedCluster(params = {}) {
        return this.post(`${CCESERVICEURL}/v2/cluster`, params);
    }

    expandClusterV2(clusterUuid, params = {}) {
        return this.post(`${CCESERVICEURL}/v2/cluster/${clusterUuid}/instances`, params);
    }

    getEventsDetail(params = {}) {
        return this.get(
            `${CCESERVICEURL}/v1/eventrecord/current?clusterUuid=${params.clusterUuid}`,
        );
    }

    getNodeEventsDetail(params = {}) {
        return this.get(`${CCESERVICEURL}/v2/event/instance/${params.instanceId}`);
    }

    preCheckCluster() {
        return this.post(
            `${CCESERVICEURL}/v2/cluster/precheck?clusterType=serverless`,
            {},
            CommonOptions,
        );
    }

    getClusterExtraInfo(clusterUuid) {
        return this.get(`${CCESERVICEURL}/v1/cluster/${clusterUuid}/extra_info`);
    }

    getClusterExtraInfoV2(clusterUuid) {
        return this.get(`${CCESERVICEURL}/v2/cluster/${clusterUuid}/extra_info`);
    }

    getTagList(params = {}) {
        return this.post('/api/tag/list', params);
    }

    syncClusterInstances(clusterUuid) {
        return this.post(`${CCESERVICEURL}/v2/sync/cluster/${clusterUuid}/instances`);
    }

    createInstanceGroup(payload) {
        return this.post(
            `${CCESERVICEURL}/v2/cluster/${payload.clusterID}/instancegroup`,
            _.omit(payload, 'clusterID'),
        );
    }

    getInstanceGroupList(payload) {
        return this.get(
            `${CCESERVICEURL}/v2/cluster/${payload.clusterID}/instancegroups`,
            _.omit(payload, 'clusterID'),
        );
    }

    getInstanceGroupDetail(clusterID, instanceGroupID, options = ClusterNotFoundOptions) {
        return this.get(
            `${CCESERVICEURL}/v2/cluster/${clusterID}/instancegroup/${instanceGroupID}`,
            {},
            options,
        ).then(res => {
            const spec = res?.result?.instanceGroup?.spec;
            if (spec?.instanceTemplate && !spec.instanceTemplates?.length) {
                spec.instanceTemplates = [spec.instanceTemplate];
            }
            spec?.instanceTemplates?.forEach(item => {
                item.relationTag = item.relationTag || false; // 后端false时没有这个key，前端补全
            });
            return res;
        });
    }

    getTasks(payload) {
        return this.get(
            `${CCESERVICEURL}/v2/tasks/${payload.taskType}`,
            _.omit(payload, 'taskType'),
        );
    }

    updateAutoScale(clusterID, instanceGroupID, payload) {
        const url = `${CCESERVICEURL}/v2/cluster/${clusterID}/instancegroup/${instanceGroupID}/autoscaler`;
        return this.put(url, payload);
    }

    deleteInstanceGroup(clusterID, instanceGroupID, payload) {
        return this.delete(
            `${CCESERVICEURL}/v2/cluster/${clusterID}/instancegroup/${instanceGroupID}`,
            payload,
        );
    }

    updateInstanceGroupReplicas(clusterID, instanceGroupID, payload) {
        return this.put(
            `${CCESERVICEURL}/v2/cluster/${clusterID}/instancegroup/${instanceGroupID}/replicas`,
            payload,
        );
    }

    getInstanceForGroup(clusterID, instanceGroupID, options) {
        options.pageNo = options.pageNo || 1;
        if (!(clusterID && instanceGroupID)) {
            return;
        }
        return this.get(
            `${CCESERVICEURL}/v2/cluster/${clusterID}/instancegroup/${instanceGroupID}/instances`,
            options,
        );
    }

    scaleDownInstanceGroup(payload = {}) {
        return this.put(
            `${CCESERVICEURL}/v2/cluster/${payload.clusterID}/instancegroup` +
                `/${payload.instanceGroupID}/scaledown`,
            _.omit(payload, 'clusterID', 'instanceGroupID'),
        );
    }

    vpcCommonWhiteList(id) {
        return this.post('/api/network/v1/commonWhiteList', {id});
    }

    combine95WhiteList() {
        return this.post('/api/eip/combine95WhiteList');
    }

    commonCheckQuota(params) {
        return this.post(`${CCESERVICEURL}/v2/check`, params);
    }

    getSubnetDetail(subnetId, options) {
        return this.get(`/api/network/v1/subnet/${subnetId}`, {}, options);
    }

    addEniSubnets(clusterUuid, payload = {}) {
        return this.put(`/api/cce/service/v2/cluster/${clusterUuid}/add_eni_subnets`, payload);
    }

    createGithubAccount(code, options = {}) {
        return this.post(`${CCECICDURL}/account/GITHUB?code=${code}`, options);
    }

    unsupportedInstanceTypes() {
        return this.get(`${CCESERVICEURL}/v2/instance/unsupported_instance_types`);
    }

    getSpecAvailableStatus() {
        return this.post(`/api/cce/artifact-service/v1/machine-specs`);
    }

    imageSupportedOs(payload) {
        return this.post(`${CCESERVICEURL}/v2/image/supported_os`, payload);
    }

    // 获取bcc镜像列表
    getImageListOnCreate(param = {product: 'BCC'}, options = {}) {
        if (BCC_IMAGE_LIST_DATA) {
            return Promise.resolve(BCC_IMAGE_LIST_DATA);
        }
        return this.post('/api/bcc/image/list/forCreateInstance', param, options).then(res => {
            BCC_IMAGE_LIST_DATA = res;
            return Promise.resolve(res);
        });
    }

    // 获取bcc侧IAM角色列表
    getRolesListByBcc() {
        return this.get('/api/bcc/instance/role', {}, CommonOptions);
    }

    // 重装的时候获取可用镜像
    getImageListOnRebuild(params) {
        return this.post('/api/bcc/image/list/correlation', params);
    }

    getBecInstanceList(payload = {}) {
        return this.get('/api/bec/service/v1/vm/instance', payload);
    }

    getBecInstanceDetail(vmId) {
        return this.get(`/api/bec/service/v1/vm/instance/${vmId}`);
    }

    queryCpt1Price(payload) {
        return this.post('/api/price/cpt1', payload);
    }
    queryCpt2Price(payload) {
        return this.post('/api/price/cpt2', payload);
    }

    updateGroupConfig(payload) {
        return this.put(
            `${CCESERVICEURL}/v2/cluster/${payload.clusterID}/` +
                `instancegroup/${payload.cceInstanceGroupID}/instancetemplate`,
            _.omit(payload, 'clusterID', 'cceInstanceGroupID'),
        );
    }

    // serviceGroup 边缘节点组列表
    getNodegroupsList(payload) {
        return this.get(`${BEC_SERVICE_GROUP_URL}/v1/nodegroups`, payload);
    }

    // serviceGroup 边缘节点组关联
    getNodegroupsRelation(payload) {
        return this.get(
            `${BEC_SERVICE_GROUP_URL}/v1/nodegroups/${payload.name}/relation`,
            _.omit(payload, 'name'),
        );
    }

    // serviceGroup 创建边缘节点组
    createNodegroups(payload) {
        return this.post(
            `${BEC_SERVICE_GROUP_URL}/v1/nodegroups?clusterID=${payload.clusterID}`,
            _.omit(payload, 'clusterID'),
        );
    }

    // serviceGroup 边缘节点组详情
    getNodegroupsDetail(clusterUuid, name) {
        return this.get(
            `${BEC_SERVICE_GROUP_URL}/v1/nodegroups/${name}?clusterID=${clusterUuid}`,
            {},
        );
    }

    // serviceGroup 修改边缘节点组
    updateNodegroups(payload) {
        return this.put(
            `${BEC_SERVICE_GROUP_URL}/v1/nodegroups/${payload.name}?clusterID=${payload.clusterID}`,
            _.omit(payload, 'clusterID'),
        );
    }

    // serviceGroup 删除边缘节点组
    deleteNodegroups(payload) {
        return this.delete(
            `${BEC_SERVICE_GROUP_URL}/v1/nodegroups/${payload.name}?clusterID=${payload.clusterID}`,
            _.omit(payload, 'name', 'clusterID'),
        );
    }

    getBatchDeployments(payload) {
        let path = `${BEC_SERVICE_GROUP_URL}${BATCH_DEPLOYMENTS}`;
        if (payload.namespace !== 'all') {
            path += `/${payload.namespace}`;
        }
        return this.get(path, _.omit(payload, 'namespace'));
    }

    getBatchDeploymentsDetail(payload) {
        return this.get(
            `${BEC_SERVICE_GROUP_URL}${BATCH_DEPLOYMENTS}/${payload.namespace}/${payload.name}`,
            {clusterID: payload.clusterID},
        );
    }

    getBatchDeploymentsYamlDetail(payload) {
        return this.get(
            `${BEC_SERVICE_GROUP_URL}${BATCH_DEPLOYMENTS}/${payload.namespace}/${payload.name}/yaml`,
            {clusterID: payload.clusterID},
        );
    }

    deleteBatchDeployments(payload) {
        const prefix = `${BEC_SERVICE_GROUP_URL}${BATCH_DEPLOYMENTS}`;
        return this.delete(
            `${prefix}/${payload.namespace}/${payload.name}?clusterID=${payload.clusterID}`,
        );
    }

    updateBatchDeployments(payload) {
        const prefix = `${BEC_SERVICE_GROUP_URL}${BATCH_DEPLOYMENTS}`;
        return this.put(
            `${prefix}/${payload.namespaceName}/${payload.name}?clusterID=${payload.clusterUuid}`,
            payload.content,
        );
    }

    createBatchDeployments(payload) {
        // 接口暂时不支持批量创建
        const prefix = `${BEC_SERVICE_GROUP_URL}${BATCH_DEPLOYMENTS}`;
        return this.post(
            `${prefix}/${payload.namespaceName}?clusterID=${payload.clusterUuid}`,
            payload.contentJson[0],
        );
    }

    getBatchDeploymentsWorkloads(payload) {
        const prefix = `${BEC_SERVICE_GROUP_URL}${BATCH_DEPLOYMENTS}`;
        return this.get(
            `${prefix}/${payload.namespaceName}/${payload.name}/workloads`,
            _.omit(payload, 'namespaceName', 'name'),
        );
    }

    // 服务访问组 - 获取列表
    getSGVisitList(payload) {
        let path = `${BEC_SERVICE_GROUP_URL}${SERVICE_GROUP_ACCESS}`;
        if (payload.namespace !== 'all') {
            path += `/${payload.namespace}`;
        }
        return this.get(path, _.omit(payload, 'namespace'));
    }
    // 服务访问组 - 创建服务
    createSGVisit(param) {
        const prefix = `${BEC_SERVICE_GROUP_URL}${SERVICE_GROUP_ACCESS}`;
        return this.post(
            `${prefix}/${param.namespace}?clusterID=${param.clusterID}`,
            _.omit(param, 'clusterID'),
        );
    }
    // 服务访问组 - 详情信息
    getSGVisitDetail(param) {
        return this.get(
            `${BEC_SERVICE_GROUP_URL}${SERVICE_GROUP_ACCESS}/${param.namespaceName}/${param.name}`,
            {
                clusterID: param.clusterUuid,
            },
        );
    }
    // 服务访问组 - 删除服务
    deleteSGVisit(namespace, name, clusterID) {
        return this.delete(`${BEC_SERVICE_GROUP_URL}${SERVICE_GROUP_ACCESS}/${namespace}/${name}`, {
            clusterID,
        });
    }
    // 服务访问组 - 更新服务
    updateSGVisit(namespace, name, param) {
        return this.put(
            `${BEC_SERVICE_GROUP_URL}${SERVICE_GROUP_ACCESS}` +
                `/${namespace}/${name}?clusterID=${param.clusterID}`,
            _.omit(param, 'clusterID'),
        );
    }
    // 服务访问组列表展示LoadBalancer
    getAccessGroupLoadBalancer(payload) {
        return this.get(
            `${BEC_SERVICE_GROUP_URL}/v1/services/namespace` +
                `/${payload.namespace}/${payload.name}/loadbalancers`,
            _.omit(payload, 'namespace', 'name'),
        );
    }
    // 边缘节点批量更新密码
    batchUpdateVm(payload) {
        return this.put('/api/bec/service/v1/vm/instance/batch/update', payload);
    }

    // 详情页修改边缘节点自治开关状态
    changeDetialEdgeAutonomyStatus(payload) {
        return this.put(
            `${BEC_SERVICE_GROUP_URL}/v1/${payload.type}/${payload.namespace}` +
                `/${payload.name}?clusterUuid=${payload.clusterUuid}`,
            {isEdgeAutonomy: payload.isEdgeAutonomy},
        );
    }

    createDiagnosis(clusterUuid, params) {
        return this.post(`/api/cce/probe/v2/cluster/${clusterUuid}/diagnosis`, params);
    }

    getDiagnosis(clusterUuid, params) {
        return this.get(`/api/cce/probe/v2/cluster/${clusterUuid}/diagnoses`, params);
    }

    getDiagDetail(clusterUuid, taskId) {
        return this.get(`/api/cce/probe/v2/cluster/${clusterUuid}/diagnosis/${taskId}/report`);
    }

    // 巡检报告
    getReceiveTypes(clusterUuid) {
        return this.get(`/api/cce/probe/v2/cluster/${clusterUuid}/inspection_reporter_type`);
    }

    getAutoInspecConfig(clusterUuid) {
        return this.get(`/api/cce/probe/v2/cluster/${clusterUuid}/inspection_plan`);
    }

    setAutoInspecConfig(clusterUuid, params) {
        return this.put(`/api/cce/probe/v2/cluster/${clusterUuid}/inspection_plan`, params);
    }

    getInspecList(params) {
        return this.get(
            `/api/cce/probe/v2/cluster/${params.clusterUuid}/inspections`,
            _.omit(params, 'clusterUuid'),
        );
    }

    getInspecConfig(clusterUuid) {
        return this.get(`/api/cce/probe/v2/cluster/${clusterUuid}/inspection_items`);
    }

    setInsepcConfig(clusterUuid, params) {
        return this.put(`/api/cce/probe/v2/cluster/${clusterUuid}/inspection_items`, params);
    }

    getLatestReport(clusterUuid) {
        return this.get(`/api/cce/probe/v2/cluster/${clusterUuid}/inspection_latest_report`);
    }

    getCurrentInspecStatus(clusterUuid) {
        return this.get(`/api/cce/probe/v2/cluster/${clusterUuid}/inspection_is_running`);
    }

    startInspecTask(clusterUuid) {
        return this.post(`/api/cce/probe/v2/cluster/${clusterUuid}/inspection`);
    }

    getInspecReportDetail(clusterUuid, taskId) {
        return this.get(`/api/cce/probe/v2/cluster/${clusterUuid}/inspection/${taskId}/report`);
    }

    // 资源对象浏览器
    getResourceObjData(clusterUuid, onlyCustomResource) {
        if (onlyCustomResource) {
            return this.get(`/api/cce/app/crd?clusterUuid=${clusterUuid}`);
        } else {
            return this.get(`/api/cce/app/apiresource?clusterUuid=${clusterUuid}`);
        }
    }

    getResourceObjList(params) {
        const {group, version, kind, namespace, clusterUuid, pageNo, pageSize, name} = params;
        let url = `/api/cce/app/crd/${group}/${version}/${kind}/${namespace}?clusterUuid=${clusterUuid}&page=${pageNo}&itemsPerPage=${pageSize}`;
        if (name) {
            url += `&filterBy=name,${name}`;
        }
        return this.get(url);
    }
    deleteResource(params) {
        return this.post(
            `/api/cce/app/_raw/${params.kind}?clusterUuid=${params.clusterUuid}`,
            params,
        );
    }
    createResource(params) {
        return this.post(
            `/api/cce/app/appdeploymentfromfile?clusterUuid=${params.clusterUuid}`,
            params,
        );
    }
    editResource(params, yamlData) {
        const {group, version, name, kind, namespace, clusterUuid} = params;
        if (params.namespace) {
            return this.put(
                `/api/cce/app/crd/_raw/${group}/${version}/kind/${kind}/namespace/${namespace}/name/${name}?clusterUuid=${clusterUuid}`,
                yamlData,
            );
        }
        return this.put(
            `/api/cce/app/crd/_raw/${group}/${version}/kind/${kind}/name/${name}?clusterUuid=${clusterUuid}`,
            yamlData,
        );
    }
    /**
     * 创建队列
     *
     * @param params
     * @param xhrOptions
     * @returns {*}
     */
    createQueue(params = {}, xhrOptions = {}) {
        let options = {
            content: params.content,
        };
        return this.post(`${CCEAIQUEUE_V1}/${params.clusterUuid}/aiqueue`, options);
    }

    // 更新队列
    updateQueueJob(param, payload) {
        return this.put(`${CCEAIQUEUE_V1}/${param.clusterUuid}/aiqueue/${param.name}`, payload);
    }

    // 删除队列
    deleteQueueJob(payload) {
        return this.delete(`${CCEAIQUEUE_V1}/${payload.clusterUuid}/aiqueue/${payload.name}`);
    }

    // 查询独占共享接口
    getCapability(clusterUuid, options = {}) {
        return this.get(`${CCEAIQUEUE_V1}/${clusterUuid}/aiqueue/capability`, {}, options);
    }

    // AI HPC队列列表接口
    getAiQueueList(payload) {
        return this.get(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aiqueue/list`,
            _.omit(payload, 'clusterUuid'),
            CommonOptions,
        );
    }

    getContainerGroupDetail(payload, options = {}) {
        return this.get(
            `${CCESERVICEURL}/v2/cluster/${payload.clusterUuid}` +
                `/instance/${payload.instanceID}?includeK8SNode=true&isVKNode=${payload.isVKNode}`,
            {},
            options,
        );
    }

    getQueueJob(payload) {
        return this.get(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aiqueue/joblist`,
            {},
            CommonOptions,
        );
    }

    // AI HPC任务列表
    getAiJobList(payload) {
        return this.get(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aijob`,
            _.omit(payload, 'clusterUuid'),
            CommonOptions,
        );
    }

    // 添加任务
    addAiJob(clusterUuid, payload) {
        return this.post(
            `${CCEAIQUEUE_V1}/${clusterUuid}/aijob`,
            _.omit(payload, 'clusterUuid'),
            CommonOptions,
        );
    }

    // 添加任务（aijobv2）
    addAiJobv2(clusterUuid, formData) {
        return this.post(`${CCEAIQUEUE_V1}/${clusterUuid}/aijobv2`, formData);
    }

    // 获取框架
    getAIJobKinds(clusterID) {
        return this.get(`${CCEAIQUEUE_V1}/${clusterID}/misc/aijobKinds`);
    }

    // 获取任务详情
    getJobDetail(payload) {
        const {clusterUuid, k8sName, kind, k8sNamespace} = payload;
        return this.get(
            `${CCESERVICEURL}/v2/cluster/${clusterUuid}/aijob` +
                `/${k8sName}?kind=${kind}&namespace=${k8sNamespace}`,
        );
    }

    scaleAiJob(clusterUuid, k8sName, kind, k8sNamespace, payload) {
        return this.post(
            `${CCEAIQUEUE_V1}/${clusterUuid}/aijob` +
                `/${k8sName}/scale?kind=${kind}&namespace=${k8sNamespace}`,
            payload,
        );
    }

    // 删除任务
    deleteAiJob(payload) {
        return this.delete(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aijob/${payload.k8sName}` +
                `?kind=${payload.kind}&namespace=${payload.k8sNamespace}`,
        );
    }

    // 获取任务详情
    getAiJobDetail(payload) {
        return this.get(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aijob/${payload.k8sName}` +
                `?kind=${payload.kind}&namespace=${payload.k8sNamespace}`,
        );
    }

    // 获取监控数据
    getAiJobMonitor(payload) {
        const {type, clusterUuid, namespaceName, podName, startTime, endTime, k8sName, step} =
            payload;
        let query = '';
        const allQuery = k8sName + '-.+-\\\\d+$';
        switch (type) {
            case 'cpu':
                query =
                    'sum(rate(container_cpu_usage_seconds_total{job="user-cluster-node",' +
                    `clusterID="${clusterUuid}",namespace=~"${namespaceName}",` +
                    `pod=~"${podName || allQuery}",container!="POD"}[5m]))`;
                break;
            case 'memory':
                query =
                    'sum(container_memory_rss{job="user-cluster-node",' +
                    `clusterID="${clusterUuid}",namespace=~"${namespaceName}",` +
                    `pod=~"${podName || allQuery}",container!="POD"}) / 1048576`;
                break;
            case 'gpu':
                query =
                    'sum(node_container_gpu_compute_utilization{job="user-cluster-gpu-node",' +
                    `clusterID="${clusterUuid}",pod_namespace=~"${namespaceName}",` +
                    `pod_name=~"${podName || allQuery}",container_name!="POD"})`;
                break;
            case 'gpuMemory':
                query =
                    'sum(node_container_gpu_compute_used{job="user-cluster-gpu-node",' +
                    `clusterID="${clusterUuid}",pod_namespace=~"${namespaceName}",` +
                    `pod_name=~"${podName || allQuery}",container_name!="POD"})`;
                break;
        }
        const params = {
            query: query || payload.query,
            start: startTime,
            end: endTime,
        };
        step && (params.step = step);
        return this.post(
            `/api/cce/service/v2/monitor/query_range?clusterID=${clusterUuid}`,
            params,
        );
    }

    // AI 任务容错事件列表
    getAiJobEventList(payload) {
        return this.get(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aijobv2/${payload.jobId}/ftevent/list`,
            _.omit(payload, ['clusterUuid', 'jobId']),
        );
    }

    // CCR企业版实例列表
    getCCRInstanceList(payload) {
        return this.get(`${CCR_COMPANY_SERVICE}/instances`, payload);
    }

    // CCR企业版命名空间
    getCCRCompanyProjects(payload) {
        return this.get(
            `${CCR_COMPANY_SERVICE}/instances/${payload.instanceId}/projects`,
            _.omit(payload, 'instanceId'),
        );
    }

    // CCR企业版镜像仓库列表
    getCCRCompanyRepositoryList(payload) {
        return this.get(
            `${CCR_COMPANY_SERVICE}/instances/${payload.instanceId}/projects/${payload.projectName}/repositories`,
            _.omit(payload, 'instanceId', 'projectName'),
        );
    }

    // CCR企业版镜像仓库列表（全部命名空间）
    getAllCCRCompanyRepositoryList(payload) {
        return this.get(
            `${CCR_COMPANY_SERVICE}/instances/${payload.instanceId}/repositories`,
            _.omit(payload, 'instanceId', 'projectName'),
        );
    }

    // 检查用户是否存在
    checkCCRUser() {
        return this.get(`${CCR_SERVICE}/user/check`, null, {
            headers: {region: 'bj'},
        });
    }

    /**
     * 检查是否开通ccr
     */
    checkCCRActivation() {
        return this.get(`${CCR_SERVICE}/activation/check`, null, {
            headers: {region: 'bj'},
        });
    }

    // CCR个人版命名空间
    getIndividualProjects() {
        return this.get(`${CCR_SERVICE}/projects`, null, {
            headers: {region: 'bj'},
        });
    }

    // CCR用户镜像列表
    // CCR是全局服务 region固定bj
    getCcrRepositoriesUserList(payload) {
        return this.get(`${CCR_SERVICE}/repositories/user`, payload, {
            headers: {region: 'bj'},
        });
    }

    // 获取百度智能云镜像列表
    getBCEImage(payload) {
        return this.get(`${CCR_SERVICE}/repositories/bce`, payload, {
            headers: {region: 'bj'},
        });
    }

    // 获取CCR企业版镜像版本列表
    getCCRCompanyRepositoryTags(payload) {
        return this.get(
            `${CCR_COMPANY_SERVICE}/instances/${payload.instanceId}` +
                `/projects/${payload.projectName}/repositories/${payload.repositoryName}/tags`,
            _.omit(payload, 'instanceId', 'projectName', 'repositoryName'),
        );
    }

    // 获取CCR个人版镜像版本列表
    getCCRRepositoryTags(payload) {
        payload.repoName = payload.repositoryName;
        delete payload.repositoryName;

        return this.get(`${CCR_SERVICE}/repositories/tags`, payload, {
            headers: {region: 'bj'},
        });
    }

    // 获取公共镜像版本列表
    getPublicRepositoryTags(payload) {
        payload.repoName = payload.repositoryName;
        delete payload.repositoryName;

        return this.get(`${CCR_SERVICE}/repositories/public/tags`, payload, {
            headers: {region: 'bj'},
        });
    }

    getBesList(payload) {
        return this.post('/api/bes/deploy/list', payload);
    }

    getBesDetail(payload) {
        return this.post('/api/bes/deploy/detail/new', payload);
    }

    // 更新标签、污点
    updateNode(payload) {
        return this.put(
            `${CCEAPPURL}/node/${payload.nodeName}?clusterUuid=${payload.clusterUuid}`,
            _.omit(payload, 'nodeName', 'clusterUuid'),
        );
    }

    // 获取deployment 历史版本
    getDeployemntHistory(payload = {}) {
        return this.get(
            `${CCEAPPURL}/deployment/${payload.namespace}/${payload.name}/oldreplicaset` +
                `?clusterUuid=${payload.clusterUuid}`,
        );
    }

    // 获取历史版本的yaml
    getHistoryYaml(payload = {}) {
        return this.get(
            `${CCEAPPURL}/_raw/replicaset/namespace/${payload.namespace}` +
                `/name/${payload.name}?clusterUuid=${payload.clusterUuid}`,
        );
    }

    // yaml版本回滚
    rollbackVersion(payload = {}) {
        return this.put(
            `${CCEAPPURL}/deployment/${payload.namespace}/${payload.name}/rollout/${payload.revision}` +
                `?clusterUuid=${payload.clusterUuid}`,
        );
    }

    // bos 合并计费白名单
    getEipCombine95WhiteList(param) {
        return this.post('/api/eip/combine95WhiteList', param, CommonOptions.silent);
    }

    // BGP_S展示后付费-按流量计费白名单
    isBgpsNetrafficWhite(data, options = {}) {
        return this.post('/api/eip/bgpsNetrafficWhiteList', data, options);
    }

    // eip quota
    getEipBandwidthQuota(param, options = {}) {
        return this.post('/api/eip/maxBandQuota', param, options);
    }

    // 静态代播线路白名单
    getEipstaticRouteWhiteList(param = ['Static', 'ChinaTelcom', 'ChinaUnicom', 'ChinaMobile']) {
        return this.post('/api/eip/staticRouteWhiteList', param, CommonOptions.silent);
    }

    getBgpBlackList() {
        return this.post('/api/eip/bgpBlackList', {}, CommonOptions.silent);
    }

    // BGP_S白名单
    isBgpsWhite(data, options = {}) {
        return this.get('/api/eip/isBgpsRegion', data, options);
    }

    getEipQuotaFromEip(param, options = {}) {
        return this.get('/api/eip/quotaV2', param, options);
    }

    getMonitorData(payload) {
        return this.post(
            `/api/cce/service/v2/monitor/query_range?clusterID=${payload.clusterUuid}`,
            {
                query: payload.query,
                start: payload.startTime,
                end: payload.endTime,
            },
        );
    }

    /**
     * 删除workflow(升级master等)
     * @param {*} {clusterID, workflowID} clusterID: string workflowID: string
     * @return {*}
     * @memberof CceClient
     */
    deleteWorkflow({clusterID, workflowID}) {
        return this.delete(`${CCESERVICEURL}/v2/cluster/${clusterID}/workflow/${workflowID}`);
    }

    /**
     * 暂停/恢复(升级master等)
     * @param {*} {clusterID} string
     * @param {*} {workflowID} string
     * @param {*} {action} 'pause' | 'resume
     * @return {*}
     * @memberof CceClient
     */
    suspendResumeWorkflow({clusterID, workflowID, action}) {
        return this.put(`${CCESERVICEURL}/v2/cluster/${clusterID}/workflow/${workflowID}`, {
            action,
        });
    }

    getK8sVersion(clusterID, clusterRole) {
        return this.get(
            `${CCESERVICEURL}/v2/cluster/${clusterID}/workflow/${clusterRole}/target_k8s_version`,
        );
    }

    checkCpromIam() {
        return this.post('/api/iam/sts/role/query', {
            roleName: 'BceServiceRole_cprom',
        });
    }

    getCpromMonitorIframe(clusterID) {
        return this.get(`/api/cce/service/v2/grafana/meta?clusterID=${clusterID}`);
    }

    createWorkflow(clusterID, payload, options) {
        return this.post(`${CCESERVICEURL}/v2/cluster/${clusterID}/workflow`, payload, options);
    }

    updateCompLogStatus(clusterID, payload, options) {
        return this.post(
            `${CCESERVICEURL}/v2/cluster/${clusterID}/addon/enablelog`,
            payload,
            options,
        );
    }
    /**
     * 查询workflow(升级master等)
     * @param {*} clusterID string
     * @param {*} workflowID string
     * @return {*} Promise<Workflow>
     */
    getWorkflow({clusterID, workflowID}) {
        return this.get(`${CCESERVICEURL}/v2/cluster/${clusterID}/workflow/${workflowID}`);
    }
    /**
     * 创建前置检查workflow
     * @param {*} payload
     * @return {*} Promse<{workflowID}>
     */
    createPrecheckWorkflow(payload) {
        return this.post(`${CCESERVICEURL}/v2/cluster/workflow/precheck`, payload);
    }

    /**
     * 查询前置检查workflow
     * @param {*} workflowID
     * @return {*} Promise<Workflow>
     */
    getPrecheckWorkflow(workflowID) {
        return this.get(`${CCESERVICEURL}/v2/cluster/workflow/precheck/${workflowID}`);
    }

    /**
     * 暂停/恢复(升级master等)
     * @param {*} {workflowID} string
     * @param {*} {action} 'pause' | 'resume
     * @return {*}
     * @memberof CceClient
     */
    suspendResumeWorkflowCreate({workflowID, action}) {
        return this.put(`${CCESERVICEURL}/v2/cluster/workflow/precheck/${workflowID}`, {
            action,
        });
    }

    // 创建数据集
    createDataset(payload = {}) {
        return this.post(`${CCEAIQUEUE_V1}/${payload.clusterUuid}/aidata/dataset`, payload);
    }

    // 数据集列表
    getAiDataset(payload = {}) {
        const namespace = payload.namespace === 'all' ? '' : payload.namespace;
        payload.namespace = namespace;
        return this.get(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aidata/dataset`,
            payload,
            CommonOptions,
        );
    }

    // 删除数据集
    deleteAiDataset(payload = {}) {
        return this.delete(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aidata/dataset/` +
                `${payload.name}?namespace=${payload.namespace}`,
        );
    }

    // 开启加速
    openRuntime(payload = {}) {
        return this.post(`${CCEAIQUEUE_V1}/${payload.clusterUuid}/aidata/runtime`, payload);
    }

    // 关闭加速
    closeRuntime(payload = {}) {
        return this.delete(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aidata/runtime/${payload.name}` +
                `?namespace=${payload.namespace}&type=${payload.kind}`,
        );
    }

    // 数据预热
    dataload(payload = {}) {
        return this.post(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aidata/dataset/` +
                `${payload.name}/dataload?namespace=${payload.namespace}`,
            payload,
        );
    }

    // 数据集详情
    getDatasetDetail(payload = {}) {
        return this.get(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aidata/dataset/` +
                `${payload.name}?namespace=${payload.namespace}`,
        );
    }

    // 预热任务列表
    getDataloadList(payload = {}) {
        return this.get(
            `${CCEAIQUEUE_V1}/${payload.clusterUuid}/aidata/dataset` +
                `/${payload.name}/dataload?namespace=${payload.namespace}`,
        );
    }

    // 节点组修改
    modifyNodeGroup(payload = {}) {
        return this.put(
            `${CCEAIQUEUE}/${payload.clusterID}/instancegroup/${payload.instanceGroupID}/configure`,
            payload,
        );
    }
    uploadAiJobCodeFile(clusterID, params) {
        return this.post(`${CCEAIQUEUE_V1}/${clusterID}/ai/uploadCode`, params);
    }

    // 获取数据集列表
    getDatasetList(clusterId, params) {
        return this.get(`${CCEAIQUEUE_V1}/${clusterId}/aidata/dataset`, params);
    }

    getGrafanaService() {
        return this.get(`/api/cprom/service/v1/grafana`);
    }

    // 获取cprom介入列表
    getCpromMonitor(clusterID) {
        return this.get(
            `/api/cce/service/v2/monitor/binding_cprom_instances?clusterID=${clusterID}`,
        );
    }

    getCpromInstanceList(payload = {}, config = {}) {
        return this.get(`/api/cprom/service/v1/instances`, payload, config);
    }

    // 获取 pfs 实例列表
    getPfsInstance(params) {
        return this.get(PFS_INSTANCE, params);
    }

    // 获取 pfs 挂载服务列表
    getPfsMountTargetList(params) {
        return this.get('/api/pfs/mountTargetList', params, {'x-silent': true});
    }

    getClusterRemainResource(clusterID) {
        return this.get(
            `/api/cce/ai-service/v1/cluster/${clusterID}/remain-resource`,
            {},
            CommonOptions,
        );
    }

    clusterAddon(params = {}) {
        const {clusterId, item, values} = params;
        return this.post(`/api/cce/service/v2/cluster/${clusterId}/addon`, {
            name: item.name,
            params: values,
        });
    }

    editClusterDeleteStatus(clusterId, payload) {
        return this.put(`/api/cce/service/v2/cluster/${clusterId}/forbiddelete`, payload);
    }

    editClusterDetectionStatus(clusterId, payload) {
        return this.put(`/api/cce/service/v2/cluster/${clusterId}/pdssource`, payload);
    }

    editClusterExposedPublic(clusterId, payload) {
        return this.put(`/api/cce/service/v2/cluster/${clusterId}/mastereip`, payload);
    }

    // BLS 相关接口
    // https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/32f3e0ed053a637db3b44c7459d4150d7c611fd1/DOC/%E8%AE%BE%E8%AE%A1%E6%96%87%E6%A1%A3/%E5%8F%AF%E8%A7%82%E6%B5%8B%E6%80%A7/%E5%AE%B9%E5%99%A8%E6%97%A5%E5%BF%97/CCE%E6%8E%A5%E5%85%A5BLS-API%E6%96%87%E6%A1%A3.md?from=review
    // 获取日志集列表
    getBLSLogStores(clusterId, payload) {
        return this.get(`/api/cce/service/v2/cluster/${clusterId}/logstores`, payload);
    }

    createBLSLogConfig(clusterId, payload) {
        return this.post(
            `/api/cce/service/v2/cluster/${clusterId}/logconfigs`,
            payload,
            CommonOptions,
        );
    }

    updateBLSLogConfig(clusterId, ns, name, payload) {
        return this.put(
            `/api/cce/service/v2/cluster/${clusterId}/logconfigs/${ns}/${name}`,
            payload,
            CommonOptions,
        );
    }

    deleteBLSLogConfig(clusterId, ns, name, payload) {
        return this.delete(
            `/api/cce/service/v2/cluster/${clusterId}/logconfigs/${ns}/${name}`,
            payload,
        );
    }

    getBLSLogConfigList(payload) {
        return this.get(`/api/cce/service/v2/cluster/${payload.clusterUuid}/logconfigs`, payload);
    }

    getLogStoreDetail(param = {}, options = {}, method = 'POST') {
        return this.post(`${v2BlsLogUrl}/logstore/detail`, param, options, method);
    }

    getLogQuery(param = {}, options = {}, method = 'POST') {
        return this.post(`${v3BlsLogUrl}/logstore/query`, param, options, method);
    }

    // 日志查询 新
    getOriginLog(param = {}, options = {}, method = 'POST') {
        return this.post(`${v3BlsLogUrl}/logstore/match`, param, options, method);
    }

    // 趋势图查询 新
    getTrend(param = {}, options = {}, method = 'POST') {
        return this.post(`${v3BlsLogUrl}/logstore/histogram`, param, options, method);
    }

    getIndexesDetail(param = {}, options = {}, method = 'POST') {
        return this.post(`${v2BlsLogUrl}/logstore/index/detail`, param, options, method);
    }

    getIndexesDelete(param = {}, options = {}, method = 'POST') {
        return this.post(`${v2BlsLogUrl}/logstore/index/delete`, param, options, method);
    }

    getIndexesCreate(param = {}, options = {}, method = 'POST') {
        return this.post(`${v2BlsLogUrl}/logstore/index/create`, param, options, method);
    }

    getIndexesUpdate(param = {}, options = {}, method = 'POST') {
        return this.post(`${v2BlsLogUrl}/logstore/index/update`, param, options, method);
    }

    getIndexesPreview(param = {}, options = {}, method = 'POST') {
        return this.post(`${v2BlsLogUrl}/logstore/preview`, param, options, method);
    }

    getContext(param = {}, options = {}, method = 'POST') {
        return this.post(`${v2BlsLogUrl}/logstore/context`, param, options, method);
    }

    getAccountDetail() {
        return this.post('/api/iam/account/detail');
    }

    getCompAdapterK8sVersions(k8sVersion, components) {
        return this.get(`/api/cce/service/v2/addons?k8sVersion=${k8sVersion}&addons=${components}`);
    }

    getExpandDomainQUOTA(region) {
        return this.get(
            `/api/quota_center/v1/quota_center/QUOTA/BLB/${region}?name=blbListenerCertQuota`,
            {},
            {'x-silent': true},
        );
    }

    getQuota(params) {
        const {product, region, name = ''} = params || {};
        return this.get(
            `/api/quota_center/v1/quota_center/QUOTA/${product}/${region}`,
            {name},
            {'x-silent': true},
        );
    }

    // 组件管理start
    getCompsData(clusterId, names) {
        return this.get(
            `/api/cce/service/v2/cluster/${clusterId}/addon` + (names ? `?addons=${names}` : ''),
        );
    }

    installComp(clusterId, payload) {
        return this.post(`/api/cce/service/v2/cluster/${clusterId}/addon`, payload);
    }

    uninstallComp(clusterId, payload) {
        return this.delete(`/api/cce/service/v2/cluster/${clusterId}/addon`, null, {
            data: payload,
        });
    }

    upgradeComp(clusterId, payload) {
        return this.post(`/api/cce/service/v2/cluster/${clusterId}/addon/upgrade`, payload);
    }

    updateComp(clusterId, payload) {
        return this.put(`/api/cce/service/v2/cluster/${clusterId}/addon`, payload);
    }
    // 组件管理end

    // 查询命名空间资源配额
    getNamespaceResourceQuota(params) {
        const {namesapce, clusterId} = params;
        return this.get(`/api/cce/app/resourcequota/${namesapce}?clusterUuid=${clusterId}`);
    }

    // 创建和更新命名空间资源配额
    updateNamespaceResourceQuota(params) {
        const {clusterId, namespace, name, spec} = params;
        return this.post(`/api/cce/app/resourcequota?clusterUuid=${clusterId}`, {
            namespace,
            name,
            spec,
        });
    }

    // 查询命名空间资源限制
    getNamespaceResourceLimit(params) {
        const {namesapce, clusterId} = params;
        return this.get(`/api/cce/app/limitrange/${namesapce}?clusterUuid=${clusterId}`);
    }

    // 创建和更新命名空间资源限制
    updateNamespaceResourceLimit(params) {
        const {clusterId, namespace, name, spec} = params;
        return this.post(`/api/cce/app/limitrange?clusterUuid=${clusterId}`, {
            namespace,
            name,
            spec,
        });
    }
    // 节点容器组列表
    getNodePodList(params) {
        return this.get(`/api/cce/app/pod`, params);
    }

    getPodMetrics(params) {
        return this.get(`/api/cce/app/metrics`, params, CommonOptions);
    }

    // 开通集群审计推送到bls
    openBlsAudit(payload) {
        return this.post(
            `/api/cce/monitor/audit/deploy?clusterUuid=${payload.clusterUuid}`,
            {},
            {'x-silent': true},
        );
    }

    // 关闭集群审计推送到bls
    closeBlsAudit(payload) {
        return this.post(`/api/cce/monitor/audit?clusterUuid=${payload.clusterUuid}&Action=delete`);
    }

    // 更新集群审计推送到bls
    updateBlsAudit(payload) {
        return this.post(`/api/cce/monitor/audit?clusterUuid=${payload.clusterUuid}&Action=update`);
    }

    // 获取工作负载名称（根据命名空间和工作负载类型）
    getWorkLoadNames(payload) {
        const url = payload.nameSpace
            ? `${CCEAPPURL}/${payload.workType}/${payload.nameSpace}`
            : `${CCEAPPURL}/${payload.workType}`;
        return this.get(url, queryString(payload));
    }

    // 获取Pod
    getMonitorPods(payload) {
        return this.get(
            `${CCEAPPURL}/${payload.workType}/${payload.nameSpace}/${payload.workName}?clusterUuid=${payload.clusterUuid}`,
        );
    }

    // 获取事件分布
    getEventDistribution(payload) {
        return this.post(
            `/api/cce/monitor/event/event_distribution?clusterUuid=${payload.clusterUuid}`,
            payload,
        );
    }

    // 异常资源对象分布
    getAbnormalResource(payload) {
        return this.post(
            `/api/cce/monitor/event/abnormal_resource?clusterUuid=${payload.clusterUuid}`,
            payload,
        );
    }

    // 异常节点、Pod分布
    getAbnormalObject(payload) {
        return this.post(
            `/api/cce/monitor/event/abnormal_object?clusterUuid=${payload.clusterUuid}`,
            payload,
        );
    }

    // 挂载Volume失败数量
    getFailedVolume(payload) {
        return this.post(
            `/api/cce/monitor/event/failed_volume?clusterUuid=${payload.clusterUuid}`,
            payload,
        );
    }

    // 镜像拉取失败数量
    getFailedImage(payload) {
        return this.post(
            `/api/cce/monitor/event/failed_image?clusterUuid=${payload.clusterUuid}`,
            payload,
        );
    }

    // 事件类型趋势
    getEventTrend(payload) {
        return this.post(
            `/api/cce/monitor/event/event_trend?clusterUuid=${payload.clusterUuid}`,
            payload,
        );
    }

    // 异常事件趋势
    getAbnormalEventTrend(payload) {
        return this.post(
            `/api/cce/monitor/event/abnormal_trend?clusterUuid=${payload.clusterUuid}`,
            payload,
        );
    }

    // 校验安全组
    checkSecurityGroup(payload) {
        return this.post(`/api/cce/service/v2/cluster/check_security_groups`, payload);
    }

    // 更新worker安全组
    changeWorkerSecurity(payload) {
        return this.put(
            `/api/cce/service/v2/cluster/${payload.clusterUuid}/default_node_security_groups`,
            payload,
        );
    }

    // iam激活服务
    openIamService(param, options) {
        return this.post('/api/iam/sts/role/activate', param, {headers: options});
    }

    // 校验审计bls是否创建成功
    checkAuditBls(payload) {
        return this.get(
            `/api/cce/monitor/audit/bls_logstore_status?clusterUuid=${payload.clusterUuid}`,
        );
    }

    // 检查用户bls的权限
    getBlsPolicy(payload) {
        return this.get(`/api/cce/monitor/audit/user_bce_policy`, payload);
    }

    // 缩容保护
    scaleDown(clusterID, payload) {
        return this.put(`/api/cce/service/v2/cluster/${clusterID}/instanceScaleDown`, payload);
    }

    // 节点排水
    nodeDrain(payload) {
        return this.post('/api/cce/service/v2/nodes/drain', payload);
    }
    // 更新集群名称
    updateClusterName(payload) {
        return this.put(`${CCEAIQUEUE}/${payload.id}/clusterName`, payload);
    }

    // 创建备份仓库
    creatBackupRepository(payload) {
        return this.post(`/api/cce/backuprepository/v1/backuprepositorys`, payload);
    }

    // 查询备份仓库列表
    getBackupRepositoryList(payload) {
        return this.get(`/api/cce/backuprepository/v1/backuprepositorys`, payload);
    }

    // 删除备份仓库
    deleteBackupRepository(id) {
        return this.delete(`/api/cce/backuprepository/v1/backuprepositorys/${id}`);
    }

    // 创建恢复任务
    creatRestoreTask(payload) {
        return this.post(`${CCEAIQUEUE}/${payload.clusterUuid}/restoreTasks`, payload);
    }

    // 查询恢复任务列表
    getRestoreTaskList(payload) {
        return this.get(`${CCEAIQUEUE}/${payload.clusterUuid}/restoreTasks`, payload);
    }

    // 删除恢复任务
    deleteRestoreTask(payload) {
        return this.delete(`${CCEAIQUEUE}/${payload.clusterUuid}/restoreTasks/${payload.id}`);
    }
    // 获取资源对象数据
    getBackResourceObjList(clusterUuid) {
        return this.get(`/api/cce/app/apiresource?clusterUuid=${clusterUuid}`);
    }

    // 获取corntab中文释义
    getCornTabText(payload) {
        return this.get(
            `/api/cce/service/v2/cluster/${payload.clusterUuid}/backupScheduleRules/scheduleDescribe`,
            payload,
        );
    }

    // 更新节点组安全组
    changeNodeGroupSecurity(payload) {
        return this.put(
            `/api/cce/service/v2/cluster/${payload.clusterUuid}/instancegroup/${payload.instanceGroupID}/securityGroups`,
            payload,
        );
    }

    // 更新ENI安全组
    changeEniSecurity(payload) {
        return this.put(
            `/api/cce/service/v2/cluster/${payload.clusterUuid}/default_eni_security_groups`,
            payload,
        );
    }
    // 创建备份任务
    createBackTask(payload) {
        return this.post(`${CCEAIQUEUE}/${payload.clusterUuid}/backuptasks`, payload);
    }

    // 删除备份任务
    deleteBackTask(payload) {
        return this.delete(
            `${CCEAIQUEUE}/${payload.clusterUuid}/backuptasks/${payload.backupTaskID}`,
        );
    }

    // 删除定时备份策略
    deleteTimeBackTask(payload) {
        return this.delete(
            `${CCEAIQUEUE}/${payload.clusterUuid}/backupScheduleRules/${payload.backupTaskID}`,
        );
    }

    // 创建定时备份任务
    createBackScheduleTask(payload) {
        return this.post(`${CCEAIQUEUE}/${payload.clusterUuid}/backupScheduleRules`, payload);
    }

    // 查询备份任务列表
    getBackTaskList(payload) {
        return this.get(`${CCEAIQUEUE}/${payload.clusterUuid}/backuptasks`, payload);
    }

    // 查询特定仓库下所有备份任务列表，跨集群
    getAllBackTaskList(repositoryID) {
        return this.get(`/api/cce/service/v2/backuprepositorys/${repositoryID}/getbackuptasks`);
    }

    // 查询备份任务详情
    getBackTaskDetails(repositoryID, backupName) {
        return this.get(
            `/api/cce/service/v2/backuprepositorys/${repositoryID}/getbackuptasks/${backupName}`,
        );
    }

    // 查询定时备份任务列表
    getTimeBackTaskList(payload) {
        return this.get(`${CCEAIQUEUE}/${payload.clusterUuid}/backupScheduleRules`, payload);
    }

    // 获取加速芯片类型列表
    getGpuTypes(payload) {
        return this.get(`/api/cce/service/v2/cluster/${payload.clusterID}/gpuTypes`, payload);
    }

    // 集群GPU检查获取节点列表
    getCheckNodeList(payload) {
        return this.get(
            `/api/cce/probe/v2/cluster/${payload.clusterUuid}/check_instances`,
            payload,
        );
    }

    // 集群GPU检查获取检查项
    getCheckConfigList(payload) {
        return this.get(
            `/api/cce/probe/v2/cluster/${payload.clusterUuid}/check_items?probeSource=CCECluster`,
        );
    }

    // 获取集群GPU检查报告列表
    getGpuCheckList(payload) {
        return this.get(`/api/cce/probe/v2/cluster/${payload.clusterUuid}/checks`, payload);
    }

    // 创建集群GPU检查任务
    createGpuCheck(payload) {
        return this.post(`/api/cce/probe/v2/cluster/${payload.clusterUuid}/check`, payload);
    }

    // 获取集群GPU检查报告详情
    getGpuReportDetail(payload) {
        return this.get(
            `/api/cce/probe/v2/cluster/${payload.clusterUuid}/check/${payload.taskId}/report`,
        );
    }

    // 获取cron hpa列表
    getCronHpaList(payload) {
        return this.get(`/api/cce/app/cronhorizontalpodautoscaler/${payload.namespace}`, payload);
    }

    // 创建cron hpa
    createCronHpa(payload) {
        return this.post(
            `/api/cce/app/cronhorizontalpodautoscaler?clusterUuid=${payload.clusterUuid}`,
            payload,
        );
    }

    // 更新cron hpa
    updateCronHpa(payload) {
        return this.put(
            `/api/cce/app/cronhorizontalpodautoscaler/${payload.namespace}/${payload.name}?clusterUuid=${payload.clusterUuid}`,
            payload,
        );
    }
    // 查询cron hpa 详情
    getCronHpaDetail(payload) {
        return this.get(
            `/api/cce/app/cronhorizontalpodautoscaler/${payload.namespace}/${payload.name}?clusterUuid=${payload.clusterUuid}`,
        );
    }
    // 删除cron hpa
    deleteCronHpaDetail(payload) {
        return this.delete(
            `/api/cce/app/cronhorizontalpodautoscaler/${payload.namespace}/${payload.name}?clusterUuid=${payload.clusterUuid}`,
        );
    }

    getEbcVolumeList(payload, clusterUuid) {
        return this.post(`${CCEAIQUEUE}/${clusterUuid}/instances/listVolumes`, payload);
    }

    //迁移计划
    getCceMigrationTaskDetail(planId, taskId) {
        return this.get(`/api/cce/migration/v2/plan/${planId}/task/${taskId}`, {}, CommonOptions);
    }

    // 批量创建诊断任务
    batchCreateDiagnosis(clusterUuid, params) {
        // 只传递body参数，避免拼接多余query参数
        return this.post(`/api/cce/probe/v2/cluster/${clusterUuid}/diagnoses`, params);
    }

    //更新迁移集群信息
    updateCceMigrationTask(planId, taskId, payload) {
        return this.put(
            `/api/cce/migration/v2/plan/${planId}/task/${taskId}/config`,
            payload,
            CommonOptions,
        );
    }

    // hpas相关接口

    getHpasFlavors(payload) {
        return this.post('/api/hpas/flavor/list', payload);
    }

    getHpasImageList() {
        return this.post('/api/hpas/image/list');
    }

    getHpasCustomImages(payload) {
        return this.post('/api/hpas/image/custom/list', payload);
    }

    getHpasEhcClusterList() {
        return this.post('/api/hpas/ehcCluster/list');
    }

    // RBAC ClusterRole 相关接口
    getClusterRoleList(params) {
        const {clusterUuid, pageNo, pageSize, keyword} = params;
        let url = `/api/cce/app/crd/rbac.authorization.k8s.io/v1/ClusterRole/?clusterUuid=${clusterUuid}&page=${pageNo}&itemsPerPage=${pageSize}`;
        if (keyword) {
            url += `&filterBy=name,${keyword}`;
        }
        return this.get(url);
    }

    getClusterRoleDetail(params) {
        const {clusterUuid, name} = params;
        return this.get(
            `/api/cce/app/crd/rbac.authorization.k8s.io/v1/ClusterRole/?clusterUuid=${clusterUuid}&filterBy=name,${name}`,
        );
    }

    getClusterRoleYaml(params) {
        const {clusterUuid, name} = params;
        return this.get(
            `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/ClusterRole/name/${name}?clusterUuid=${clusterUuid}`,
        );
    }

    createClusterRole(params) {
        return this.post(`/api/cce/app/appdeploymentfromfile?clusterUuid=${params.clusterUuid}`, {
            yaml: params.yaml,
        });
    }

    updateClusterRole(params) {
        const {clusterUuid, name, yaml} = params;
        return this.put(
            `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/ClusterRole/name/${name}?clusterUuid=${clusterUuid}`,
            yaml,
        );
    }

    deleteClusterRole(params) {
        return this.post(`/api/cce/app/_raw/ClusterRole?clusterUuid=${params.clusterUuid}`, {
            group: 'rbac.authorization.k8s.io',
            version: 'v1',
            kind: 'ClusterRole',
            name: params.name,
            clusterUuid: params.clusterUuid,
        });
    }

    // RBAC Role 相关接口
    getRoleList(params) {
        const {clusterUuid, namespace, pageNo, pageSize, keyword} = params;
        let url = `/api/cce/app/crd/rbac.authorization.k8s.io/v1/Role/${namespace}?clusterUuid=${clusterUuid}&page=${pageNo}&itemsPerPage=${pageSize}`;
        if (keyword) {
            url += `&filterBy=name,${keyword}`;
        }
        return this.get(url);
    }

    getRoleDetail(params) {
        const {clusterUuid, namespace, name} = params;
        return this.get(
            `/api/cce/app/crd/rbac.authorization.k8s.io/v1/Role/${namespace}?clusterUuid=${clusterUuid}&filterBy=name,${name}`,
        );
    }

    getRoleYaml(params) {
        const {clusterUuid, namespace, name} = params;
        return this.get(
            `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/Role/namespace/${namespace}/name/${name}?clusterUuid=${clusterUuid}`,
        );
    }

    createRole(params) {
        return this.post(`/api/cce/app/appdeploymentfromfile?clusterUuid=${params.clusterUuid}`, {
            yaml: params.yaml,
        });
    }

    updateRole(params) {
        const {clusterUuid, namespace, name, yaml} = params;
        return this.put(
            `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/Role/namespace/${namespace}/name/${name}?clusterUuid=${clusterUuid}`,
            yaml,
        );
    }

    deleteRole(params) {
        return this.post(`/api/cce/app/_raw/Role?clusterUuid=${params.clusterUuid}`, {
            group: 'rbac.authorization.k8s.io',
            version: 'v1',
            kind: 'Role',
            namespace: params.namespace,
            name: params.name,
            clusterUuid: params.clusterUuid,
        });
    }
}
export default new CceClient();
